"""Initial migration

Revision ID: ba91953420f7
Revises:
Create Date: 2025-05-02 12:10:31.249112

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision: str = 'ba91953420f7'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('games',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('date_release', sa.DateTime(), nullable=True),
    sa.Column('win', sa.Boolean(), nullable=False),
    sa.Column('mac', sa.<PERSON>(), nullable=False),
    sa.Column('linux', sa.<PERSON>(), nullable=False),
    sa.Column('rating', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('positive_ratio', sa.Integer(), nullable=True),
    sa.Column('user_reviews', sa.Integer(), nullable=True),
    sa.Column('price_final', sa.Float(), nullable=True),
    sa.Column('price_original', sa.Float(), nullable=True),
    sa.Column('discount', sa.Float(), nullable=True),
    sa.Column('steam_deck', sa.Boolean(), nullable=True),
    sa.Column('app_id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('app_id')
    )
    op.create_index(op.f('ix_games_title'), 'games', ['title'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_table('game_metadata',
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('tags_json', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('app_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['app_id'], ['games.app_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('recommendations',
    sa.Column('helpful', sa.Integer(), nullable=True),
    sa.Column('funny', sa.Integer(), nullable=True),
    sa.Column('date', sa.DateTime(), nullable=True),
    sa.Column('is_recommended', sa.Boolean(), nullable=False),
    sa.Column('hours', sa.Float(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('app_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('review_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['app_id'], ['games.app_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_recommendations_user_id'), 'recommendations', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_recommendations_user_id'), table_name='recommendations')
    op.drop_table('recommendations')
    op.drop_table('game_metadata')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_games_title'), table_name='games')
    op.drop_table('games')
    # ### end Alembic commands ###
