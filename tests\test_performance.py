"""
Тесты производительности для системы рекомендаций игр Steam
Измеряет время отклика и нагрузочную способность
"""

import sys
import os
import time
import statistics
import concurrent.futures
from typing import List, Dict, Any

# Добавляем корневую директорию в путь для импорта модулей
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.main import app
from fastapi.testclient import TestClient

class PerformanceTester:
    """Класс для тестирования производительности"""
    
    def __init__(self):
        self.client = TestClient(app)
        self.results = []
    
    def measure_endpoint(self, endpoint: str, method: str = "GET", data: Dict = None, iterations: int = 10):
        """Измерение времени отклика эндпоинта"""
        times = []
        errors = 0
        
        print(f"🔄 Тестирование {method} {endpoint} ({iterations} итераций)...")
        
        for i in range(iterations):
            start_time = time.time()
            try:
                if method == "GET":
                    response = self.client.get(endpoint)
                elif method == "POST":
                    response = self.client.post(endpoint, json=data)
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # в миллисекундах
                
                if response.status_code < 400:
                    times.append(response_time)
                else:
                    errors += 1
                    
            except Exception as e:
                errors += 1
                print(f"   ❌ Ошибка в итерации {i+1}: {str(e)}")
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            
            result = {
                "endpoint": endpoint,
                "method": method,
                "iterations": iterations,
                "successful": len(times),
                "errors": errors,
                "avg_time_ms": round(avg_time, 2),
                "min_time_ms": round(min_time, 2),
                "max_time_ms": round(max_time, 2),
                "median_time_ms": round(median_time, 2)
            }
            
            self.results.append(result)
            
            print(f"   ✅ Среднее время: {avg_time:.2f}ms")
            print(f"   📊 Мин/Макс: {min_time:.2f}ms / {max_time:.2f}ms")
            print(f"   🎯 Медиана: {median_time:.2f}ms")
            print(f"   ❌ Ошибки: {errors}/{iterations}")
            print()
            
            return result
        else:
            print(f"   ❌ Все запросы завершились ошибкой")
            return None
    
    def test_concurrent_load(self, endpoint: str, concurrent_users: int = 5, requests_per_user: int = 10):
        """Тест нагрузки с параллельными пользователями"""
        print(f"🚀 Нагрузочный тест {endpoint}")
        print(f"   👥 Пользователей: {concurrent_users}")
        print(f"   📊 Запросов на пользователя: {requests_per_user}")
        
        def make_requests(user_id):
            times = []
            errors = 0
            
            for _ in range(requests_per_user):
                start_time = time.time()
                try:
                    response = self.client.get(endpoint)
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000
                    
                    if response.status_code < 400:
                        times.append(response_time)
                    else:
                        errors += 1
                except:
                    errors += 1
            
            return {"user_id": user_id, "times": times, "errors": errors}
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(make_requests, i) for i in range(concurrent_users)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Агрегация результатов
        all_times = []
        total_errors = 0
        total_requests = 0
        
        for result in results:
            all_times.extend(result["times"])
            total_errors += result["errors"]
            total_requests += len(result["times"]) + result["errors"]
        
        if all_times:
            avg_time = statistics.mean(all_times)
            throughput = len(all_times) / total_time
            
            print(f"   ⏱️  Общее время: {total_time:.2f}s")
            print(f"   📈 Пропускная способность: {throughput:.2f} req/s")
            print(f"   ⚡ Среднее время отклика: {avg_time:.2f}ms")
            print(f"   ✅ Успешных запросов: {len(all_times)}")
            print(f"   ❌ Ошибок: {total_errors}")
            print()
            
            return {
                "endpoint": endpoint,
                "concurrent_users": concurrent_users,
                "requests_per_user": requests_per_user,
                "total_time": round(total_time, 2),
                "throughput": round(throughput, 2),
                "avg_response_time": round(avg_time, 2),
                "successful_requests": len(all_times),
                "errors": total_errors
            }
        else:
            print(f"   ❌ Все запросы завершились ошибкой")
            return None
    
    def run_performance_tests(self):
        """Запуск всех тестов производительности"""
        print("⚡ ТЕСТИРОВАНИЕ ПРОИЗВОДИТЕЛЬНОСТИ")
        print("=" * 50)
        print()
        
        # Тесты времени отклика
        print("📊 ТЕСТЫ ВРЕМЕНИ ОТКЛИКА")
        print("-" * 30)
        
        endpoints_to_test = [
            ("/games?limit=10", "GET"),
            ("/games/search/Counter", "GET"),
            ("/games/autocomplete?query=Counter&limit=5", "GET"),
            ("/recommend/Counter-Strike?top_n=5", "GET"),
            ("/games/10", "GET"),
            ("/games/filter?year_min=2020", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            self.measure_endpoint(endpoint, method, iterations=20)
        
        # Нагрузочные тесты
        print("🚀 НАГРУЗОЧНЫЕ ТЕСТЫ")
        print("-" * 20)
        
        load_tests = [
            ("/games?limit=10", 3, 5),
            ("/games/search/Counter", 3, 5),
            ("/recommend/Counter-Strike?top_n=5", 2, 3),
        ]
        
        load_results = []
        for endpoint, users, requests in load_tests:
            result = self.test_concurrent_load(endpoint, users, requests)
            if result:
                load_results.append(result)
        
        # Итоговый отчет
        self.print_summary_report(load_results)
    
    def print_summary_report(self, load_results: List[Dict]):
        """Печать итогового отчета"""
        print("=" * 50)
        print("📋 ИТОГОВЫЙ ОТЧЕТ ПРОИЗВОДИТЕЛЬНОСТИ")
        print("=" * 50)
        
        if self.results:
            print("\n🕐 ВРЕМЯ ОТКЛИКА:")
            print(f"{'Эндпоинт':<40} {'Среднее (ms)':<12} {'Медиана (ms)':<12}")
            print("-" * 64)
            
            for result in self.results:
                endpoint = result["endpoint"][:37] + "..." if len(result["endpoint"]) > 40 else result["endpoint"]
                print(f"{endpoint:<40} {result['avg_time_ms']:<12} {result['median_time_ms']:<12}")
        
        if load_results:
            print("\n🚀 НАГРУЗОЧНЫЕ ТЕСТЫ:")
            print(f"{'Эндпоинт':<30} {'Пропускная способность':<20} {'Ошибки':<8}")
            print("-" * 58)
            
            for result in load_results:
                endpoint = result["endpoint"][:27] + "..." if len(result["endpoint"]) > 30 else result["endpoint"]
                throughput = f"{result['throughput']:.1f} req/s"
                errors = f"{result['errors']}"
                print(f"{endpoint:<30} {throughput:<20} {errors:<8}")
        
        print("\n💡 РЕКОМЕНДАЦИИ:")
        
        # Анализ результатов
        if self.results:
            slow_endpoints = [r for r in self.results if r["avg_time_ms"] > 1000]
            if slow_endpoints:
                print("   ⚠️  Медленные эндпоинты (>1000ms):")
                for endpoint in slow_endpoints:
                    print(f"      • {endpoint['endpoint']}: {endpoint['avg_time_ms']:.2f}ms")
            else:
                print("   ✅ Все эндпоинты показывают хорошую производительность (<1000ms)")
        
        if load_results:
            low_throughput = [r for r in load_results if r["throughput"] < 1.0]
            if low_throughput:
                print("   ⚠️  Низкая пропускная способность (<1 req/s):")
                for result in low_throughput:
                    print(f"      • {result['endpoint']}: {result['throughput']:.2f} req/s")
            else:
                print("   ✅ Хорошая пропускная способность для всех тестируемых эндпоинтов")


def main():
    """Основная функция"""
    print("⚡ Game Recommendation System - Performance Testing")
    print("Тестирование производительности и нагрузки")
    print()
    
    tester = PerformanceTester()
    tester.run_performance_tests()
    
    print("\n🏁 Тестирование производительности завершено!")
    return 0


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h"]:
        print("""
⚡ Game Recommendation System - Performance Testing

ИСПОЛЬЗОВАНИЕ:
    python tests/test_performance.py

ОПИСАНИЕ:
    Тестирование производительности и нагрузочной способности системы.
    Измеряет время отклика эндпоинтов и пропускную способность.

ТЕСТЫ ВКЛЮЧАЮТ:
    • Измерение времени отклика основных эндпоинтов
    • Нагрузочное тестирование с параллельными запросами
    • Анализ производительности и рекомендации

ТРЕБОВАНИЯ:
    • Запущенное приложение (python run.py)
    • Доступная база данных
        """)
        sys.exit(0)
    
    sys.exit(main())
