"""add_avatar_fields_to_users

Revision ID: 52cf970b1959
Revises: b4073cb9bf59
Create Date: 2025-05-28 14:17:33.658107

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '52cf970b1959'
down_revision: Union[str, None] = 'b4073cb9bf59'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Добавляем поля для аватара в таблицу users
    op.add_column('users', sa.Column('avatar', sa.LargeBinary(), nullable=True))
    op.add_column('users', sa.Column('avatar_content_type', sa.String(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Удаляем поля аватара из таблицы users
    op.drop_column('users', 'avatar_content_type')
    op.drop_column('users', 'avatar')
