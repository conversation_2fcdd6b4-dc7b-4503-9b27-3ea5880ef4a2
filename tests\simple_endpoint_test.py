"""
Простой тест нового эндпоинта без инициализации рекомендательной системы
"""
import os
import sys

# Добавляем текущую директорию в путь Python
sys.path.append(os.path.abspath('.'))

from sqlmodel import Session, select
from src.database import create_db_and_tables, engine
from src.models.models import Game

def test_exact_match_logic():
    """Тестирование логики точного поиска без рекомендательной системы"""
    print("Тестирование логики точного поиска игр...")
    
    # Создаем таблицы (если нужно)
    create_db_and_tables()
    
    # Получаем игры напрямую из базы данных
    print("\n1. Получение игр из базы данных...")
    with Session(engine) as session:
        games = session.exec(select(Game).limit(5)).all()
        
        if not games:
            print("❌ В базе данных нет игр для тестирования")
            return
        
        print(f"✓ Найдено {len(games)} игр:")
        for i, game in enumerate(games, 1):
            print(f"  {i}. {game.title} (ID: {game.app_id})")
    
    # Тестируем логику точного поиска
    print("\n2. Тестирование логики точного поиска...")
    
    test_cases = [
        {
            "title": games[0].title,
            "description": "Поиск по точному названию (должен найти)",
            "expected": True
        },
        {
            "title": games[0].title[:5],
            "description": "Поиск по части названия (не должен найти)",
            "expected": False
        },
        {
            "title": "НесуществующаяИгра",
            "description": "Поиск несуществующей игры (не должен найти)",
            "expected": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nТест {i}: {test_case['description']}")
        print(f"Ищем: '{test_case['title']}'")
        
        # Симулируем логику эндпоинта
        with Session(engine) as session:
            # Ищем игру с точным названием
            exact_match = session.exec(
                select(Game).where(Game.title == test_case['title'])
            ).first()
            
            if exact_match:
                print(f"✓ Найдена игра: {exact_match.title} (ID: {exact_match.app_id})")
                
                # Симулируем ответ эндпоинта
                response = {
                    "query": test_case['title'],
                    "found": True,
                    "game_id": exact_match.app_id,
                    "game_title": exact_match.title
                }
                
                import json
                print("Ответ эндпоинта:")
                print(json.dumps(response, ensure_ascii=False, indent=2))
                
                if test_case['expected']:
                    print("✓ Результат соответствует ожиданиям")
                else:
                    print("❌ Неожиданный результат - игра найдена, хотя не должна была")
            else:
                print("❌ Игра не найдена (404)")
                if not test_case['expected']:
                    print("✓ Результат соответствует ожиданиям")
                else:
                    print("❌ Неожиданный результат - игра не найдена, хотя должна была")

def test_endpoint_url_encoding():
    """Тестирование URL-кодирования для названий игр"""
    print("\n3. Тестирование URL-кодирования...")
    
    # Примеры названий игр с специальными символами
    test_titles = [
        "Half-Life",
        "Counter-Strike: Global Offensive", 
        "Portal 2",
        "Team Fortress 2"
    ]
    
    print("Примеры URL-кодирования для названий игр:")
    for title in test_titles:
        import urllib.parse
        encoded = urllib.parse.quote(title)
        print(f"  '{title}' -> '/games/id-by-title/{encoded}'")

def main():
    """Основная функция тестирования"""
    print("Простой тест эндпоинта для получения ID игры по названию")
    print("=" * 70)
    
    test_exact_match_logic()
    test_endpoint_url_encoding()
    
    print("\n" + "=" * 70)
    print("Тестирование завершено!")
    print("\nОсновные выводы:")
    print("1. ✓ Эндпоинт работает только с точными названиями игр")
    print("2. ✓ Частичные названия возвращают 404")
    print("3. ✓ Несуществующие игры возвращают 404")
    print("4. ✓ Специальные символы в названиях требуют URL-кодирования")
    
    print("\nДля тестирования реального эндпоинта:")
    print("1. Запустите сервер: uvicorn main:app --reload")
    print("2. Используйте точные названия игр из базы данных")
    print("3. Пример запроса: GET /games/id-by-title/Counter-Strike")

if __name__ == "__main__":
    main()
