from fastapi import APIRouter, HTTPException, Query, Request, Depends, status, UploadFile, File, Response
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
from typing import Dict, Any, List
from src.game_recommender_db import <PERSON><PERSON><PERSON>ommender
from src.api.steamapi import get_steam_game_image
from datetime import timed<PERSON><PERSON>
from sqlmodel import Session, select

from src.auth import (
    UserAuth, Token, authenticate_user, create_access_token,
    get_current_user, create_user, ACCESS_TOKEN_EXPIRE_MINUTES
)
from src.database import get_session
from src.models.models import (
    UserCreate as UserCreateModel,
    GameListType,
    User as UserModel
)

from src.user_ratings import add_user_rating as add_rating, delete_user_rating, get_user_ratings
from src.game_lists import add_game_to_list as add_game, remove_game_from_list, get_user_game_lists

router = APIRouter()

# Создаем глобальный экземпляр рекомендательной системы
recommender = GameRecommender()




@router.get("/recommend/{title}", tags=["Рекомендации"])
async def recommend_games_by_title(
    title: str,
    top_n: int = Query(10, ge=1, le=20)
):
    """
    Получение рекомендаций для игры по её названию

    - **title**: Название игры
    - **top_n**: Количество рекомендаций (от 1 до 20)
    """
    result = recommender.recommend_by_title(title, top_n)
    if "error" in result:
        raise HTTPException(status_code=404, detail=result["error"])
    return JSONResponse(content=result)


@router.get("/games", tags=["Игры"])
async def get_all_games(limit: int = Query(100, ge=1, le=1000)):
    """Получение списка всех игр с ограничением по количеству"""
    return JSONResponse(content=recommender.get_all_games(limit=limit))


@router.get("/games/filter/tags", tags=["Параметры фильтрации"])
async def get_available_tags(limit: int = Query(100, ge=1, le=500, description="Максимальное количество тегов для возврата")):
    """Получение списка доступных тегов для фильтрации игр"""
    return JSONResponse(content=recommender.get_available_tags(limit=limit))


@router.get("/games/filter/years", tags=["Параметры фильтрации"])
async def get_available_years():
    """Получение списка доступных годов выпуска для фильтрации игр"""
    return JSONResponse(content=recommender.get_available_years())


@router.get("/games/filter/ratings", tags=["Параметры фильтрации"])
async def get_available_ratings():
    """Получение списка доступных рейтингов для фильтрации игр"""
    return JSONResponse(content=recommender.get_available_ratings())


@router.get("/games/filter/os", tags=["Параметры фильтрации"])
async def get_available_os():
    """Получение списка доступных операционных систем для фильтрации игр"""
    return JSONResponse(content=[
        {"os": "win", "name": "Windows"},
        {"os": "mac", "name": "macOS"},
        {"os": "linux", "name": "Linux"}
    ])


@router.get("/games/filter", tags=["Игры"])
async def filter_games(
    year_min: int = Query(None, description="Минимальный год выпуска игры. Оставьте пустым, чтобы не фильтровать по минимальному году."),
    year_max: int = Query(None, description="Максимальный год выпуска игры. Оставьте пустым, чтобы не фильтровать по максимальному году."),
    tags: List[str] = Query(None, description="Список тегов игры (например, Action, RPG, Strategy). Можно указать несколько тегов. Оставьте пустым, чтобы не фильтровать по тегам."),
    os: str = Query(None, description="Операционная система: win, mac, linux. Оставьте пустым, чтобы не фильтровать по ОС."),
    rating: str = Query(None, description="Рейтинг игры (например, Very Positive, Positive, Mixed). Оставьте пустым, чтобы не фильтровать по рейтингу."),
    min_positive_ratio: int = Query(None, ge=0, le=100, description="Минимальный процент положительных отзывов (0-100). Оставьте пустым, чтобы не фильтровать по этому параметру."),
    steam_deck: bool = Query(None, description="Поддержка Steam Deck (True/False). Оставьте пустым, чтобы не фильтровать по этому параметру.")
):
    return JSONResponse(content=recommender.filter_games(
        year_min=year_min,
        year_max=year_max,
        tags=tags,
        os=os,
        rating=rating,
        min_positive_ratio=min_positive_ratio,
        steam_deck=steam_deck
    ))


@router.get("/games/search/{title}", tags=["Игры"])
async def search_games(title: str):
    """Поиск игр по названию или части названия"""
    return JSONResponse(content=recommender.search_games_by_title(title))


@router.get("/games/id-by-title/{title}", tags=["Игры"])
async def get_game_id_by_title(title: str):
    """
    Получение ID игры по её точному названию

    - **title**: Точное полное название игры

    Возвращает ID игры только при точном совпадении названия.
    Если игра с таким точным названием не найдена, возвращает ошибку 404.
    """
    try:
        # Ищем игру с точным названием в базе данных
        games_df = recommender.games
        exact_match = games_df[games_df['title'] == title]

        if exact_match.empty:
            raise HTTPException(
                status_code=404,
                detail=f"Игра с точным названием '{title}' не найдена"
            )

        # Берем первую найденную игру (должна быть только одна с точным названием)
        game = exact_match.iloc[0]

        return JSONResponse(content={
            "query": title,
            "found": True,
            "game_id": int(game["app_id"]),
            "game_title": game["title"]
        })

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка при поиске игры: {str(e)}"
        )


@router.get("/games/autocomplete", tags=["Игры"])
async def autocomplete_game_title(
    query: str = Query(..., description="Строка запроса для автокомплита"),
    limit: int = Query(10, ge=1, le=20, description="Максимальное количество результатов")
):
    """
    Автодополнение названий игр по введенному запросу

    - **query**: Строка запроса для автодополнения
    - **limit**: Максимальное количество результатов (от 1 до 20)

    Возвращает список названий игр, подходящих для автодополнения.
    Если запрос пустой или слишком короткий (менее 2 символов), возвращает популярные игры.
    """
    return JSONResponse(content=recommender.autocomplete_game_title(query, limit))


@router.get("/games/{app_id}", tags=["Игры"])
async def get_game_by_id(app_id: int):
    """Получение информации об игре по ID, включая изображение из Steam и все метаданные"""
    game = recommender.get_game_by_id(app_id)
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    # Получаем изображение игры из Steam API
    try:
        # Используем тот же подход, что и в эндпоинте get_game_image
        image_url = await get_steam_game_image(app_id)
        game['image_url'] = image_url
    except Exception as e:
        # Логируем ошибку для отладки
        print(f"Ошибка при получении изображения для игры {app_id}: {str(e)}")
        # Устанавливаем пустую строку вместо None, чтобы поведение было более предсказуемым
        game['image_url'] = ""

    # Убедимся, что все метаданные включены в ответ
    # Метаданные уже должны быть добавлены в get_game_by_id, но проверим это
    if 'description' not in game:
        game['description'] = ''
    if 'tags' not in game:
        game['tags'] = []

    return JSONResponse(content=game)


@router.get("/recommend/user/{user_id}", tags=["Рекомендации"])
async def recommend_games_for_user(
    user_id: int,
    top_n: int = Query(10, ge=1, le=20, description="Количество рекомендаций (от 1 до 20)")
):
    """
    Получение рекомендаций игр для конкретного пользователя на основе его оценок

    Использует гибридный подход для построения рекомендаций:
    1. Если у пользователя есть положительные оценки, использует коллаборативную фильтрацию
       на основе оценок пользователя из сбалансированной таблицы.
    2. Если у пользователя нет положительных оценок, рекомендует популярные игры,
       исключая те, которые пользователь уже оценил.

    - **user_id**: ID пользователя
    - **top_n**: Количество рекомендаций (от 1 до 20)
    """
    result = recommender.recommend_for_user(user_id, top_n)
    if "error" in result:
        raise HTTPException(status_code=404, detail=result["error"])
    return JSONResponse(content=result)


@router.get("/recommend/user/{user_id}/item-based", tags=["Рекомендации"])
async def recommend_games_for_user_item_based(
    user_id: int,
    top_n: int = Query(10, ge=1, le=20, description="Количество рекомендаций (от 1 до 20)")
):
    """
    Получение рекомендаций игр для конкретного пользователя на основе item-based коллаборативной фильтрации

    Использует item-based коллаборативную фильтрацию для построения рекомендаций:
    1. Для каждой положительно оцененной пользователем игры находит похожие игры
       на основе оценок других пользователей.
    2. Если у пользователя нет положительных оценок, рекомендует популярные игры,
       исключая те, которые пользователь уже оценил.

    В отличие от обычных рекомендаций, этот метод показывает, на основе какой игры
    была сделана каждая рекомендация.

    - **user_id**: ID пользователя
    - **top_n**: Количество рекомендаций (от 1 до 20)
    """
    result = recommender.recommend_for_user_item_based(user_id, top_n)
    if "error" in result:
        raise HTTPException(status_code=404, detail=result["error"])
    return JSONResponse(content=result)


@router.get("/get_game_image/{app_id}", tags=["Игры"])
async def get_game_image(app_id: int):
    """Получение URL изображения игры из Steam API"""
    try:
        image_url = await get_steam_game_image(app_id)
        return {"image_url": image_url, "app_id": app_id}
    except Exception as e:
        # Логируем ошибку для отладки
        print(f"Ошибка при получении изображения для игры {app_id}: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    

@router.post("/register", tags=["Аутентификация"], response_model=Dict[str, Any])
async def register_user(user_data: UserCreateModel, session: Session = Depends(get_session)):
    """
    Регистрация нового пользователя

    - **username**: Имя пользователя (уникальное)
    - **email**: Email пользователя (уникальный)
    - **password**: Пароль пользователя
    """
    try:
        user = create_user(user_data, session)
        return {
            "success": True,
            "message": "Пользователь успешно зарегистрирован",
            "username": user.username,
            "email": user.email,
            "id": user.id
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при регистрации: {str(e)}"
        )

@router.post("/token", tags=["Аутентификация"], response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: Session = Depends(get_session)
):
    """
    Получение токена доступа (вход в систему)

    - **username**: Имя пользователя
    - **password**: Пароль пользователя
    """
    user = authenticate_user(form_data.username, form_data.password, session)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Неверное имя пользователя или пароль",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/users/me", tags=["Пользователи"],response_model=Dict[str, Any])
async def read_users_me(current_user: UserAuth = Depends(get_current_user)):
    """
    Получение информации о текущем пользователе

    Требует авторизации через токен
    """
    return {
        "username": current_user.username,
        "email": current_user.email,
        "id": current_user.id,
        "is_active": current_user.is_active,
        "has_avatar": current_user.has_avatar,
        "avatar_content_type": current_user.avatar_content_type
    }

@router.get("/users/me/recommendations", tags=["Рекомендации"], response_model=Dict[str, Any])
async def get_recommendations_for_current_user(
    top_n: int = 10,
    current_user: UserAuth = Depends(get_current_user)
):
    """
    Получение персональных рекомендаций игр для текущего пользователя

    - **top_n**: Количество рекомендаций (по умолчанию 10)

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем рекомендации для пользователя
    recommendations = recommender.recommend_for_user(user_id, top_n)

    return recommendations



@router.post("/users/me/ratings", tags=["Пользователи"], response_model=Dict[str, Any])
async def add_rating_for_current_user(
    app_id: int,
    rating: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Добавление или обновление оценки игры для текущего пользователя

    - **app_id**: ID игры
    - **rating**: Оценка от 1 до 5

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Добавляем оценку через модуль user_ratings
    result = add_rating(user_id, app_id, rating, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )

    # Также обновляем оценку в рекомендательной системе
    recommender.add_user_rating(user_id, app_id, rating)

    return result

@router.delete("/users/me/ratings/{app_id}", tags=["Пользователи"], response_model=Dict[str, Any])
async def delete_rating_for_current_user(
    app_id: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Удаление оценки игры для текущего пользователя

    - **app_id**: ID игры

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Удаляем оценку через модуль user_ratings
    result = delete_user_rating(user_id, app_id, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["error"]
        )

    # Также удаляем оценку из рекомендательной системы
    recommender.delete_user_rating(user_id, app_id)

    return result

@router.get("/users/me/ratings", tags=["Пользователи"], response_model=Dict[str, Any])
async def get_ratings_for_current_user(
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Получение всех оценок текущего пользователя

    Требует авторизации через токен
    """
    # Используем ID пользователя из объекта пользователя
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем оценки пользователя через модуль user_ratings
    ratings = get_user_ratings(user_id, session)

    return {
        "user_id": user_id,
        "username": current_user.username,
        "ratings": ratings
    }

@router.post("/users/me/game-lists/{list_type}/{app_id}", tags=["Пользователи"], response_model=Dict[str, Any])
async def add_game_to_user_list(
    list_type: GameListType,
    app_id: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Добавление игры в список пользователя

    - **list_type**: Тип списка (playing, planned, completed)
    - **app_id**: ID игры

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Добавляем игру в список через модуль game_lists
    result = add_game(user_id, app_id, list_type, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )

    return result

@router.delete("/users/me/game-lists/{app_id}", tags=["Пользователи"], response_model=Dict[str, Any])
async def remove_game_from_user_list(
    app_id: int,
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Удаление игры из списка пользователя

    - **app_id**: ID игры

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Удаляем игру из списка через модуль game_lists
    result = remove_game_from_list(user_id, app_id, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["error"]
        )

    return result

@router.get("/users/me/game-lists", tags=["Пользователи"], response_model=Dict[str, List])
async def get_all_user_game_lists(
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Получение всех списков игр пользователя

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем все списки пользователя через модуль game_lists
    result = get_user_game_lists(user_id, session)

    # Проверяем результат
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result


@router.post("/users/me/avatar", tags=["Пользователи"], response_model=Dict[str, Any])
async def upload_avatar(
    avatar: UploadFile = File(...),
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Загрузка аватара для текущего пользователя

    - Поддерживаемые форматы: JPEG, PNG, GIF
    - Максимальный размер: 2 МБ

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Проверяем тип файла
    content_type = avatar.content_type
    if content_type not in ["image/jpeg", "image/png", "image/gif"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Поддерживаются только форматы JPEG, PNG и GIF"
        )

    # Читаем содержимое файла
    contents = await avatar.read()

    # Проверяем размер файла (максимум 2 МБ)
    if len(contents) > 2 * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Размер файла не должен превышать 2 МБ"
        )

    # Получаем пользователя из базы данных
    user = session.exec(select(UserModel).where(UserModel.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Обновляем аватар пользователя
    user.avatar = contents
    user.avatar_content_type = content_type

    # Сохраняем изменения в базе данных
    session.add(user)
    session.commit()

    return {
        "success": True,
        "message": "Аватар успешно загружен",
        "content_type": content_type
    }


@router.get("/users/{user_id}/avatar", tags=["Пользователи"])
async def get_user_avatar(
    user_id: int,
    session: Session = Depends(get_session)
):
    """
    Получение аватара пользователя по его ID

    Возвращает изображение аватара в исходном формате
    """
    # Получаем пользователя из базы данных
    user = session.exec(select(UserModel).where(UserModel.id == user_id)).first()

    if not user or not user.avatar:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Аватар не найден"
        )

    # Возвращаем изображение
    return Response(
        content=user.avatar,
        media_type=user.avatar_content_type
    )


@router.delete("/users/me/avatar", tags=["Пользователи"], response_model=Dict[str, Any])
async def delete_avatar(
    current_user: UserAuth = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Удаление аватара текущего пользователя

    Требует авторизации через токен
    """
    user_id = current_user.id

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Получаем пользователя из базы данных
    user = session.exec(select(UserModel).where(UserModel.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден в базе данных"
        )

    # Удаляем аватар
    user.avatar = None
    user.avatar_content_type = None

    # Сохраняем изменения в базе данных
    session.add(user)
    session.commit()

    return {
        "success": True,
        "message": "Аватар успешно удален"
    }