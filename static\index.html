<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Система рекомендаций игр Steam</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .search-container {
            margin: 20px 0;
            text-align: center;
        }
        input[type="text"] {
            padding: 10px;
            width: 70%;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            padding: 10px 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
        #results {
            margin-top: 20px;
        }
        .game-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .game-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .game-score {
            color: #666;
            margin-bottom: 5px;
        }
        .game-tags {
            font-size: 14px;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Система рекомендаций игр Steam</h1>

        <div style="text-align: center; margin-bottom: 20px;">
            <a href="/static/index.html" style="margin: 0 10px; color: #4CAF50; text-decoration: none;">Главная</a>
            <a href="/static/filter.html" style="margin: 0 10px; color: #4CAF50; text-decoration: none;">Фильтрация игр</a>
        </div>

        <div class="search-container">
            <input type="text" id="game-search" placeholder="Введите название игры...">
            <button id="search-btn">Найти рекомендации</button>
        </div>
        <div id="results"></div>
    </div>

    <script src="/static/api-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchBtn = document.getElementById('search-btn');
            const gameSearch = document.getElementById('game-search');
            const resultsDiv = document.getElementById('results');

            // Создаем экземпляр API-клиента
            const api = new GameRecommenderAPI();

            // Функция для автокомплита
            gameSearch.addEventListener('input', async function() {
                const query = gameSearch.value.trim();
                if (query.length < 2) return;

                try {
                    const data = await api.autocompleteGameTitle(query);

                    // Здесь можно добавить логику для отображения автокомплита
                    console.log('Autocomplete results:', data);
                } catch (error) {
                    console.error('Error fetching autocomplete:', error);
                }
            });

            // Функция для поиска рекомендаций
            searchBtn.addEventListener('click', async function() {
                const title = gameSearch.value.trim();
                if (!title) return;

                resultsDiv.innerHTML = '<p>Загрузка рекомендаций...</p>';

                try {
                    const data = await api.getRecommendations(title);

                    if (data.error) {
                        resultsDiv.innerHTML = `<p>Ошибка: ${data.error}</p>`;
                        return;
                    }

                    let html = `<h2>Рекомендации для игры "${data.matched_title}"</h2>`;

                    if (data.recommendations && data.recommendations.length > 0) {
                        data.recommendations.forEach(game => {
                            html += `
                                <div class="game-card">
                                    <div class="game-title">${game.title}</div>
                                    <div class="game-score">
                                        Гибридная оценка: ${(game.hybrid_score * 100).toFixed(1)}%
                                    </div>
                                    <div class="game-tags">
                                        Общие теги: ${game.common_tags.join(', ')}
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        html += '<p>Рекомендации не найдены</p>';
                    }

                    resultsDiv.innerHTML = html;
                } catch (error) {
                    console.error('Error fetching recommendations:', error);
                    resultsDiv.innerHTML = '<p>Произошла ошибка при получении рекомендаций</p>';
                }
            });
        });
    </script>
</body>
</html>
