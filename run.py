from src.config import settings
import uvicorn

if __name__ == "__main__":
    print(f"Запуск веб-сервера на http://{settings.HOST}:{settings.PORT}")
    print(f"Документация доступна по адресу http://{settings.HOST}:{settings.PORT}/docs")
    print(f"Режим работы: {settings.ENVIRONMENT}")
    print("Система рекомендаций игр Steam")
    print("Используется гибридный метод рекомендаций, который комбинирует:")
    print("  - Коллаборативную фильтрацию (на основе похожести игр по оценкам пользователей)")
    print("  - Контент-бейсд фильтрацию (на основе похожести тегов игр)")
    print("  - Рейтинг и популярность игр")

    # Используем строку импорта вместо объекта приложения для поддержки reload
    uvicorn.run(
        "src.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
