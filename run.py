from src.main import app
import uvicorn

if __name__ == "__main__":
    port = 8000
    print(f"Запуск веб-сервера на http://127.0.0.1:{port}")
    print(f"Документация доступна по адресу http://127.0.0.1:{port}/docs")
    print("Система рекомендаций игр Steam")
    print("Используется гибридный метод рекомендаций, который комбинирует:")
    print("  - Коллаборативную фильтрацию (на основе похожести игр по оценкам пользователей)")
    print("  - Контент-бейсд фильтрацию (на основе похожести тегов игр)")
    print("  - Рейтинг и популярность игр")
    uvicorn.run(app, host="127.0.0.1", port=port)
