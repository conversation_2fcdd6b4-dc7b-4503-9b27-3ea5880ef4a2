"""
Конфигурация приложения с использованием переменных окружения
"""
import os
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Загружаем переменные окружения из .env файла
load_dotenv()


class Settings(BaseSettings):
    """Настройки приложения"""
    
    # =============================================================================
    # SECURITY SETTINGS
    # =============================================================================
    SECRET_KEY: str = "dev-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # =============================================================================
    # SERVER SETTINGS
    # =============================================================================
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # =============================================================================
    # DATABASE SETTINGS
    # =============================================================================
    DATABASE_URL: str = "sqlite:///src/database.db"
    GAMES_DATABASE_PATH: str = "data/games.db"
    DATABASE_ECHO: bool = True
    
    # =============================================================================
    # CACHE SETTINGS
    # =============================================================================
    CACHE_DIR: str = "src/cache"
    ENABLE_CACHE: bool = True
    
    # =============================================================================
    # CORS SETTINGS
    # =============================================================================
    CORS_ORIGINS: str = "*"
    CORS_METHODS: str = "*"
    CORS_HEADERS: str = "*"
    CORS_CREDENTIALS: bool = True
    
    # =============================================================================
    # EXTERNAL APIs
    # =============================================================================
    STEAM_API_TIMEOUT: float = 10.0
    
    # =============================================================================
    # LOGGING SETTINGS
    # =============================================================================
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "simple"
    
    # =============================================================================
    # MACHINE LEARNING SETTINGS
    # =============================================================================
    MIN_USER_RATINGS: int = 5
    MIN_GAME_RATINGS: int = 5
    SVD_COMPONENTS: int = 200
    
    # =============================================================================
    # FILE UPLOAD SETTINGS
    # =============================================================================
    MAX_AVATAR_SIZE: int = 2097152  # 2MB
    ALLOWED_AVATAR_TYPES: str = "image/jpeg,image/png,image/gif"
    
    # =============================================================================
    # API SETTINGS
    # =============================================================================
    MAX_PAGE_SIZE: int = 1000
    MAX_RECOMMENDATIONS: int = 20
    API_TIMEOUT: float = 30.0
    
    # =============================================================================
    # COMPUTED PROPERTIES
    # =============================================================================
    
    @property
    def cors_origins_list(self) -> List[str]:
        """Возвращает список разрешенных CORS доменов"""
        if self.CORS_ORIGINS == "*":
            return ["*"]
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]
    
    @property
    def cors_methods_list(self) -> List[str]:
        """Возвращает список разрешенных HTTP методов"""
        if self.CORS_METHODS == "*":
            return ["*"]
        return [method.strip() for method in self.CORS_METHODS.split(",")]
    
    @property
    def cors_headers_list(self) -> List[str]:
        """Возвращает список разрешенных заголовков"""
        if self.CORS_HEADERS == "*":
            return ["*"]
        return [header.strip() for header in self.CORS_HEADERS.split(",")]
    
    @property
    def allowed_avatar_types_list(self) -> List[str]:
        """Возвращает список разрешенных типов файлов для аватаров"""
        return [mime_type.strip() for mime_type in self.ALLOWED_AVATAR_TYPES.split(",")]
    
    @property
    def is_development(self) -> bool:
        """Проверяет, запущено ли приложение в режиме разработки"""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """Проверяет, запущено ли приложение в продакшене"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_testing(self) -> bool:
        """Проверяет, запущено ли приложение в тестовом режиме"""
        return self.ENVIRONMENT.lower() == "testing"
    
    # =============================================================================
    # VALIDATORS
    # =============================================================================
    
    @validator('SECRET_KEY')
    def validate_secret_key(cls, v):
        """Проверяет, что секретный ключ достаточно длинный"""
        if len(v) < 32:
            raise ValueError('SECRET_KEY должен быть не менее 32 символов')
        return v
    
    @validator('PORT')
    def validate_port(cls, v):
        """Проверяет, что порт в допустимом диапазоне"""
        if not 1 <= v <= 65535:
            raise ValueError('PORT должен быть в диапазоне 1-65535')
        return v
    
    @validator('ENVIRONMENT')
    def validate_environment(cls, v):
        """Проверяет, что окружение имеет допустимое значение"""
        allowed = ['development', 'production', 'testing']
        if v.lower() not in allowed:
            raise ValueError(f'ENVIRONMENT должен быть одним из: {", ".join(allowed)}')
        return v.lower()
    
    @validator('LOG_LEVEL')
    def validate_log_level(cls, v):
        """Проверяет, что уровень логирования корректный"""
        allowed = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in allowed:
            raise ValueError(f'LOG_LEVEL должен быть одним из: {", ".join(allowed)}')
        return v.upper()
    
    class Config:
        """Конфигурация Pydantic"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Создаем глобальный экземпляр настроек
settings = Settings()


def get_settings() -> Settings:
    """Возвращает экземпляр настроек приложения"""
    return settings


# Для обратной совместимости экспортируем основные настройки
SECRET_KEY = settings.SECRET_KEY
ACCESS_TOKEN_EXPIRE_MINUTES = settings.ACCESS_TOKEN_EXPIRE_MINUTES
DATABASE_URL = settings.DATABASE_URL
