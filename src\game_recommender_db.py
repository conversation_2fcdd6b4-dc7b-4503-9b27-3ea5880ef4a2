import json
import pandas as pd
import numpy as np
import pickle
import os
import time
import random
import sqlite3
from typing import List, Optional, Dict, Any, Set, Tuple, Union
from difflib import get_close_matches
from sklearn.neighbors import NearestNeighbors
from sqlmodel import Session, select
from src.database import engine
from src.models.models import Game, GameMetadata, Recommendation, BalancedRecommendation
from scipy.sparse import coo_matrix
from scipy.sparse.linalg import svds

# Пути для кеширования
CACHE_DIR = "src/cache"
GAMES_CACHE = os.path.join(CACHE_DIR, "games_cache.pkl")
METADATA_CACHE = os.path.join(CACHE_DIR, "metadata_cache.pkl")
BALANCED_RECOMMENDATIONS_CACHE = os.path.join(CACHE_DIR, "balanced_recommendations_cache.pkl")
RECOMMENDATION_MATRIX_CACHE = os.path.join(CACHE_DIR, "recommendation_matrix_cache.pkl")
CACHE_INFO = os.path.join(CACHE_DIR, "cache_info.json")

# Путь к базе данных
DB_PATH = "data/games.db"

class GameRecommender:
    """Класс для рекомендации игр на основе данных из базы данных"""

    def __init__(self, use_cache=False):
        """
        Инициализирует рекомендательную систему, загружая данные из базы данных

        Args:
            use_cache: Параметр оставлен для совместимости
        """
        start_time = time.time()

        print("Загрузка данных из базы данных...")
        self._load_from_database()

        end_time = time.time()
        print(f"Инициализация завершена за {end_time - start_time:.2f} секунд")

    # Вспомогательные методы для работы с данными

    def _convert_to_years(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Преобразует даты выпуска в годы

        Args:
            df: DataFrame с данными об играх

        Returns:
            DataFrame с добавленным столбцом 'year'
        """
        df = df.copy()
        df['year'] = pd.to_datetime(df['date_release'], errors='coerce').dt.year
        return df

    def _get_game_data(self, app_id: int) -> Optional[Dict[str, Any]]:
        """
        Получает данные об игре по её ID

        Args:
            app_id: ID игры

        Returns:
            Словарь с данными об игре или None, если игра не найдена
        """
        game_data = self.games[self.games['app_id'] == app_id]
        if game_data.empty:
            return None
        return game_data.iloc[0].to_dict()

    def _get_game_metadata(self, app_id: int) -> Optional[Dict[str, Any]]:
        """
        Получает метаданные игры по её ID

        Args:
            app_id: ID игры

        Returns:
            Словарь с метаданными игры или None, если метаданные не найдены
        """
        metadata = self.metadata[self.metadata['app_id'] == app_id]
        if metadata.empty:
            return None

        result = metadata.iloc[0].to_dict()
        # Проверяем, что теги существуют и являются списком
        if 'tags' in result and not isinstance(result['tags'], list):
            result['tags'] = []
        return result

    def _normalize_tags(self, tags: List[str]) -> List[str]:
        """
        Нормализует список тегов (приводит к нижнему регистру)

        Args:
            tags: Список тегов

        Returns:
            Нормализованный список тегов
        """
        if not tags or not isinstance(tags, list):
            return []
        return [tag.lower() for tag in tags if tag]

    def _format_game_for_response(self, game_data: Dict[str, Any], include_tags: bool = False) -> Dict[str, Any]:
        """
        Форматирует данные об игре для ответа API

        Args:
            game_data: Данные об игре
            include_tags: Включать ли теги в ответ

        Returns:
            Отформатированный словарь с данными об игре
        """
        result = {
            'app_id': int(game_data['app_id']),
            'title': game_data['title']
        }

        # Добавляем дополнительные поля, если они есть
        if 'rating' in game_data:
            result['rating'] = game_data['rating']
        if 'positive_ratio' in game_data:
            result['positive_ratio'] = float(game_data['positive_ratio']) if pd.notna(game_data['positive_ratio']) else None
        if 'user_reviews' in game_data:
            result['user_reviews'] = int(game_data['user_reviews']) if pd.notna(game_data['user_reviews']) else 0

        # Добавляем информацию о том, есть ли игра в системе рекомендаций
        result['in_recommendation_system'] = game_data['app_id'] in self.game_to_idx

        # Добавляем теги, если нужно
        if include_tags:
            metadata = self._get_game_metadata(game_data['app_id'])
            if metadata and 'tags' in metadata:
                result['tags'] = metadata['tags'][:5]  # Ограничиваем количество тегов для краткости
            else:
                result['tags'] = []

        return result

    def _handle_error(self, error: Exception, context: str) -> Dict[str, Any]:
        """
        Обрабатывает ошибку и возвращает стандартный ответ с ошибкой

        Args:
            error: Объект исключения
            context: Контекст ошибки (для логирования)

        Returns:
            Словарь с информацией об ошибке
        """
        error_message = f"Ошибка при {context}: {str(error)}"
        print(error_message)
        return {"error": error_message}

    def _is_cache_available(self):
        """Проверяет, доступен ли кеш"""
        basic_cache_available = (os.path.exists(GAMES_CACHE) and
                                os.path.exists(METADATA_CACHE))

        # Проверяем, доступен ли кеш сбалансированных рекомендаций и матрицы рекомендаций
        recommendations_cache_available = (os.path.exists(BALANCED_RECOMMENDATIONS_CACHE) and
                                          os.path.exists(RECOMMENDATION_MATRIX_CACHE))

        return basic_cache_available and recommendations_cache_available

    def _load_from_cache(self):
        """Загружает данные из кеша"""
        try:
            # Загружаем игры
            with open(GAMES_CACHE, 'rb') as f:
                self.games = pickle.load(f)
            print(f"Загружено {len(self.games)} игр из кеша")

            # Загружаем метаданные
            with open(METADATA_CACHE, 'rb') as f:
                self.metadata = pickle.load(f)
            print(f"Загружено {len(self.metadata)} метаданных из кеша")

            # Загружаем сбалансированные рекомендации
            if os.path.exists(BALANCED_RECOMMENDATIONS_CACHE):
                with open(BALANCED_RECOMMENDATIONS_CACHE, 'rb') as f:
                    self.recommendations = pickle.load(f)
                print(f"Загружено {len(self.recommendations)} сбалансированных рекомендаций из кеша")
            else:
                raise Exception("Кэш сбалансированных рекомендаций не найден")

            # Загружаем матрицу рекомендаций
            with open(RECOMMENDATION_MATRIX_CACHE, 'rb') as f:
                recommendation_data = pickle.load(f)

            # Устанавливаем атрибуты из кеша
            self.user_game_matrix_sparse = recommendation_data['user_game_matrix_sparse']
            self.knn = recommendation_data['knn']
            self.game_to_idx = recommendation_data['game_to_idx']
            self.idx_to_game = recommendation_data['idx_to_game']
            self.popular_games = recommendation_data['popular_games']
            self.has_recommendation_model = recommendation_data['has_recommendation_model']

            print(f"Загружена матрица рекомендаций с {len(self.popular_games)} играми")

            # Загружаем информацию о кеше
            if os.path.exists(CACHE_INFO):
                with open(CACHE_INFO, 'r') as f:
                    cache_info = json.load(f)
                print(f"Кеш создан: {cache_info.get('created_at')}")
                print(f"Количество игр в системе рекомендаций: {cache_info.get('popular_games_count')}")

        except Exception as e:
            print(f"Ошибка при загрузке данных из кеша: {str(e)}")
            print("Загрузка данных из базы данных...")
            self._load_from_database()

    def _load_from_database(self):
        """
        Загружает все данные из базы данных
        """
        # Загружаем данные из базы данных
        with Session(engine) as session:
            # Загружаем игры
            self.games = pd.DataFrame([game.model_dump() for game in session.exec(select(Game))])
            print(f"Загружено {len(self.games)} игр из базы данных")

            # Загружаем метаданные
            metadata_records = session.exec(select(GameMetadata)).all()
            self.metadata = pd.DataFrame([
                {
                    "app_id": record.app_id,
                    "description": record.description,
                    "tags": json.loads(record.tags_json) if record.tags_json else []
                }
                for record in metadata_records
            ])
            print(f"Загружено {len(self.metadata)} метаданных из базы данных")

            # Загружаем сбалансированные рекомендации из базы данных
            print("Загрузка сбалансированных рекомендаций из базы данных...")
            balanced_recommendations = session.exec(select(BalancedRecommendation)).all()
            if balanced_recommendations:
                self.recommendations = pd.DataFrame([rec.model_dump() for rec in balanced_recommendations])
                print(f"Загружено {len(self.recommendations)} сбалансированных рекомендаций из базы данных")
            else:
                print("В базе данных нет сбалансированных рекомендаций")
                self.recommendations = pd.DataFrame(columns=['app_id', 'user_id', 'review_id', 'is_recommended', 'helpful', 'funny', 'date', 'hours'])
                print("Создан пустой DataFrame для рекомендаций. Система рекомендаций не будет работать корректно.")

        # Создаем матрицу пользователь-игра для рекомендаций
        self._prepare_recommendation_matrix()

    def _prepare_recommendation_matrix(self):
        """
        Подготавливает матрицу пользователь-игра для рекомендаций,
        используя всю сбалансированную таблицу оценок без ограничений на количество пользователей и игр
        """
        try:
            # Объединяем данные о рекомендациях с данными об играх
            merged = pd.merge(
                self.recommendations,
                self.games[['app_id', 'title']],
                on='app_id',
                how='inner'
            )

            print(f"Исходное количество записей: {len(merged)}")

            # Подсчитываем количество оценок для каждого пользователя и игры
            user_counts = merged['user_id'].value_counts()
            game_counts = merged['app_id'].value_counts()

            # Устанавливаем минимальное количество оценок для пользователей и игр
            # для фильтрации шума и улучшения качества рекомендаций
            min_user_ratings = 5
            min_game_ratings = 5

            # Получаем активных пользователей и популярные игры
            active_users = user_counts[user_counts >= min_user_ratings].index
            popular_games = game_counts[game_counts >= min_game_ratings].index

            print(f"Количество активных пользователей: {len(active_users)}")
            print(f"Количество популярных игр: {len(popular_games)}")

            # Фильтруем данные, оставляя только активных пользователей и популярные игры
            filtered_merged = merged[
                merged['user_id'].isin(active_users) &
                merged['app_id'].isin(popular_games)
            ]

            if filtered_merged.empty or active_users.empty or popular_games.empty:
                raise ValueError("Недостаточно данных для создания модели рекомендаций")

            print(f"Количество записей после фильтрации: {len(filtered_merged)}")

            # Создаем отображения ID игр и пользователей в индексы матрицы
            self.game_to_idx = {game_id: i for i, game_id in enumerate(popular_games)}
            self.idx_to_game = {i: game_id for game_id, i in self.game_to_idx.items()}
            self.popular_games = popular_games
            user_to_idx = {user_id: i for i, user_id in enumerate(active_users)}

            # Преобразуем ID игр и пользователей в индексы матрицы
            filtered_merged.loc[:, 'row'] = filtered_merged['app_id'].map(self.game_to_idx)
            filtered_merged.loc[:, 'col'] = filtered_merged['user_id'].map(user_to_idx)

            # Используем значения is_recommended как веса в матрице
            # Преобразуем булевы значения в числа (True -> 1, False -> -1)
            filtered_merged.loc[:, 'value'] = filtered_merged['is_recommended'].astype(int) * 2 - 1

            # Создаем разреженную матрицу игра-пользователь
            data = filtered_merged['value'].values
            rows = filtered_merged['row'].values
            cols = filtered_merged['col'].values

            self.user_game_matrix_sparse = coo_matrix(
                (data, (rows, cols)),
                shape=(len(popular_games), len(active_users))
            ).tocsr()

            print(f"Создана матрица размером {self.user_game_matrix_sparse.shape}")

            # Создаем модель SVD для латентных факторов
            # Используем всю матрицу для SVD без ограничений
            try:
                print(f"Используем полную матрицу размером {self.user_game_matrix_sparse.shape} для SVD")

                # Используем всю матрицу для SVD
                svd_matrix = self.user_game_matrix_sparse
                self.svd_idx_to_game_id = {i: game_id for i, game_id in enumerate(popular_games)}
                self.game_id_to_svd_idx = {game_id: i for i, game_id in enumerate(popular_games)}

                # Вычисляем SVD с оптимальным значением k
                # Используем большее значение k для лучшего качества рекомендаций
                k = min(200, min(svd_matrix.shape) - 1)
                print(f"Вычисляем SVD с k={k}")

                # Используем более эффективную реализацию SVD для больших матриц
                try:
                    # Пробуем использовать ARPACK для больших матриц
                    U, sigma, Vt = svds(svd_matrix, k=k, algorithm='arpack')
                    print("Использован алгоритм ARPACK для SVD")
                except:
                    # Если ARPACK не работает, используем обычный SVD
                    U, sigma, Vt = svds(svd_matrix, k=k)
                    print("Использован стандартный алгоритм для SVD")

                # Сохраняем латентные факторы игр
                self.game_latent_matrix = U

                # Создаем модель ближайших соседей для латентных факторов
                self.knn_svd = NearestNeighbors(metric='cosine', algorithm='brute')
                self.knn_svd.fit(self.game_latent_matrix)

                self.using_svd = True
                print("SVD модель успешно создана")
            except Exception as e:
                print(f"Ошибка при создании SVD модели: {str(e)}")
                self.using_svd = False
                print("SVD модель не создана, будет использоваться только базовая модель")

            # Создаем основную модель ближайших соседей
            self.knn = NearestNeighbors(metric='cosine', algorithm='brute')
            self.knn.fit(self.user_game_matrix_sparse)
            self.has_recommendation_model = True

            print("Модель рекомендаций успешно создана")

        except Exception as e:
            print(f"Ошибка при создании матрицы рекомендаций: {str(e)}")
            self.popular_games = self.games['app_id'].values[:100]
            self.game_to_idx = {game_id: i for i, game_id in enumerate(self.popular_games)}
            self.idx_to_game = {i: game_id for game_id, i in self.game_to_idx.items()}
            self.has_recommendation_model = False
            self.using_svd = False


    def recommend_by_title(self, game_title: str, top_n: int = 10) -> Dict[str, Any]:
        """
        Рекомендует игры на основе названия игры, используя гибридный метод

        Args:
            game_title: Название игры
            top_n: Количество рекомендаций
        """
        try:
            # Получаем список всех названий игр
            titles = self.games['title'].tolist()

            # Ищем похожие названия
            matches = get_close_matches(game_title, titles, n=1, cutoff=0.6)
            if not matches:
                return {"error": f"No game found matching title '{game_title}'"}

            matched_title = matches[0]
            game_data = self.games[self.games['title'] == matched_title]

            if game_data.empty:
                return {"error": f"Game '{matched_title}' not found in database."}

            game_id = game_data.iloc[0]['app_id']

            # Получаем метаданные игры для использования в рекомендациях
            game_metadata = self._get_game_metadata(game_id)
            game_tags = []
            if game_metadata and 'tags' in game_metadata and game_metadata['tags']:
                game_tags = self._normalize_tags(game_metadata['tags'])

            # Используем гибридный метод, комбинируем коллаборативную и контент-бейсд фильтрацию
            # Получаем рекомендации по тегам
            content_recommendations = self.recommend_by_tags(game_id, top_n * 3)  # Берем больше рекомендаций для комбинирования

            # Проверяем, есть ли игра в системе рекомендаций для коллаборативной фильтрации
            if game_id in self.game_to_idx and self.has_recommendation_model:
                # Получаем индекс игры в матрице
                matrix_index = self.game_to_idx[game_id]

                # Получаем больше рекомендаций, чем нужно, для комбинирования
                extra_neighbors = min(200, len(self.popular_games) - 1)

                # Проверяем, можем ли мы использовать SVD-модель
                if hasattr(self, 'using_svd') and self.using_svd and game_id in self.game_id_to_svd_idx:
                    # Используем SVD-модель для получения рекомендаций
                    svd_index = self.game_id_to_svd_idx[game_id]
                    game_vector = self.game_latent_matrix[svd_index].reshape(1, -1)
                    distances, indices = self.knn_svd.kneighbors(game_vector, n_neighbors=min(extra_neighbors + 1, len(self.game_latent_matrix)))

                    # Преобразуем индексы SVD в индексы полной матрицы
                    similar_indices = []
                    for idx in indices.squeeze().tolist()[1:]:  # Пропускаем первый элемент (это сама игра)
                        game_id_from_svd = self.svd_idx_to_game_id[idx]
                        if game_id_from_svd in self.game_to_idx:
                            similar_indices.append(self.game_to_idx[game_id_from_svd])

                    similar_distances = distances.squeeze().tolist()[1:]  # Пропускаем первый элемент

                    print(f"Используем SVD-модель для гибридных рекомендаций. Найдено {len(similar_indices)} похожих игр.")
                else:
                    # Используем обычную модель для получения рекомендаций
                    distances, indices = self.knn.kneighbors(self.user_game_matrix_sparse[matrix_index].toarray(), n_neighbors=extra_neighbors + 1)

                    # Преобразуем в списки и удаляем первый элемент (это сама игра)
                    similar_indices = indices.squeeze().tolist()[1:]
                    similar_distances = distances.squeeze().tolist()[1:]

                    print(f"Используем обычную модель для гибридных рекомендаций. Найдено {len(similar_indices)} похожих игр.")

                # Создаем словарь для быстрого поиска коллаборативных рекомендаций
                collab_recommendations = {}
                for idx, dist in zip(similar_indices, similar_distances):
                    app_id = self.idx_to_game[idx]
                    game_data = self.games[self.games['app_id'] == app_id]

                    if not game_data.empty:
                        title = game_data.iloc[0]['title']
                        positive_ratio = game_data.iloc[0]['positive_ratio'] if 'positive_ratio' in game_data.iloc[0] else 0
                        user_reviews = game_data.iloc[0]['user_reviews'] if 'user_reviews' in game_data.iloc[0] else 0

                        # Получаем метаданные рекомендуемой игры
                        rec_metadata = self._get_game_metadata(app_id)
                        rec_tags = []
                        if rec_metadata and 'tags' in rec_metadata and rec_metadata['tags']:
                            rec_tags = self._normalize_tags(rec_metadata['tags'])

                        # Вычисляем сходство по тегам
                        tag_similarity = 0.0
                        if game_tags and rec_tags:
                            common_tags = set(game_tags) & set(rec_tags)
                            if common_tags:
                                tag_similarity = len(common_tags) / max(len(game_tags), len(rec_tags))

                        # Добавляем небольшую случайность для разнообразия
                        randomness = np.random.uniform(0.95, 1.05)

                        collab_recommendations[app_id] = {
                            'app_id': int(app_id),
                            'title': title,
                            'collab_score': (1 - dist) * randomness,  # Добавляем случайность
                            'tag_similarity': tag_similarity,
                            'positive_ratio': positive_ratio,
                            'user_reviews': user_reviews,
                            'tags': rec_tags
                        }

                # Комбинируем рекомендации
                hybrid_candidates = []

                # Добавляем рекомендации из контент-бейсд фильтрации
                for rec in content_recommendations:
                    app_id = rec['app_id']
                    # Проверяем, есть ли эта игра также в коллаборативных рекомендациях
                    if app_id in collab_recommendations:
                        # Если да, комбинируем оценки
                        collab_score = collab_recommendations[app_id]['collab_score']
                        content_score = rec['score']
                        tag_similarity = collab_recommendations[app_id]['tag_similarity']
                        positive_ratio = collab_recommendations[app_id]['positive_ratio']
                        user_reviews = collab_recommendations[app_id]['user_reviews']

                        # Гибридная оценка: учитываем больше факторов и добавляем случайность
                        hybrid_score = (
                            collab_score * 0.35 +
                            content_score * 0.35 +
                            tag_similarity * 0.1 +
                            (positive_ratio/100) * 0.1 +
                            (np.log1p(user_reviews) / 20) * 0.05 +
                            np.random.uniform(0, 0.05)  # Добавляем небольшую случайность
                        )

                        hybrid_candidates.append({
                            'app_id': app_id,
                            'title': rec['title'],
                            'hybrid_score': hybrid_score,
                            'content_score': content_score,
                            'collab_score': collab_score,
                            'tag_similarity': tag_similarity,
                            'common_tags': rec['common_tags']
                        })
                    else:
                        # Если нет, добавляем только с контент-бейсд оценкой
                        game_data = self.games[self.games['app_id'] == app_id]
                        if not game_data.empty:
                            positive_ratio = game_data.iloc[0]['positive_ratio'] if 'positive_ratio' in game_data.iloc[0] else 0
                            user_reviews = game_data.iloc[0]['user_reviews'] if 'user_reviews' in game_data.iloc[0] else 0

                            # Получаем метаданные рекомендуемой игры
                            rec_metadata = self._get_game_metadata(app_id)
                            rec_tags = []
                            if rec_metadata and 'tags' in rec_metadata and rec_metadata['tags']:
                                rec_tags = self._normalize_tags(rec_metadata['tags'])

                            # Вычисляем сходство по тегам
                            tag_similarity = 0.0
                            if game_tags and rec_tags:
                                common_tags = set(game_tags) & set(rec_tags)
                                if common_tags:
                                    tag_similarity = len(common_tags) / max(len(game_tags), len(rec_tags))

                            # Гибридная оценка с нулевой коллаборативной составляющей
                            hybrid_score = (
                                0 * 0.35 +
                                rec['score'] * 0.35 +
                                tag_similarity * 0.1 +
                                (positive_ratio/100) * 0.1 +
                                (np.log1p(user_reviews) / 20) * 0.05 +
                                np.random.uniform(0, 0.05)  # Добавляем небольшую случайность
                            )

                            hybrid_candidates.append({
                                'app_id': app_id,
                                'title': rec['title'],
                                'hybrid_score': hybrid_score,
                                'content_score': rec['score'],
                                'collab_score': 0,
                                'tag_similarity': tag_similarity,
                                'common_tags': rec['common_tags']
                            })

                # Добавляем оставшиеся коллаборативные рекомендации, которых нет в контент-бейсд
                for app_id, rec in collab_recommendations.items():
                    if not any(candidate['app_id'] == app_id for candidate in hybrid_candidates):
                        # Гибридная оценка с нулевой контент-бейсд составляющей
                        hybrid_score = (
                            rec['collab_score'] * 0.35 +
                            0 * 0.35 +
                            rec['tag_similarity'] * 0.1 +
                            (rec['positive_ratio']/100) * 0.1 +
                            (np.log1p(rec['user_reviews']) / 20) * 0.05 +
                            np.random.uniform(0, 0.05)  # Добавляем небольшую случайность
                        )

                        hybrid_candidates.append({
                            'app_id': app_id,
                            'title': rec['title'],
                            'hybrid_score': hybrid_score,
                            'content_score': 0,
                            'collab_score': rec['collab_score'],
                            'tag_similarity': rec['tag_similarity'],
                            'common_tags': []
                        })

                # Сортируем кандидатов по гибридной оценке
                hybrid_candidates.sort(key=lambda x: x['hybrid_score'], reverse=True)

                # Берем только top_n кандидатов
                top_hybrid = hybrid_candidates[:top_n]

                # Формируем результат
                recommendations = []
                for rec in top_hybrid:
                    recommendations.append({
                        'app_id': int(rec['app_id']),
                        'title': rec['title'],
                        'hybrid_score': float(rec['hybrid_score']),
                        'content_score': float(rec['content_score']),
                        'collab_score': float(rec['collab_score']),
                        'common_tags': rec['common_tags']
                    })

                return {
                    "input_title": game_title,
                    "matched_title": matched_title,
                    "recommendations": recommendations,
                    "method": "hybrid"
                }
            else:
                # Если игры нет в системе рекомендаций, используем расширенный метод
                # Получаем популярные игры с похожими тегами
                popular_games_with_tags = []

                # Получаем популярные игры
                popular_game_ids = self.popular_games[:500]  # Берем больше игр для разнообразия

                # Перемешиваем список для разнообразия
                np.random.shuffle(popular_game_ids)

                # Фильтруем игры с похожими тегами
                for pop_game_id in popular_game_ids:
                    if pop_game_id == game_id:
                        continue

                    # Получаем метаданные популярной игры
                    pop_metadata = self._get_game_metadata(pop_game_id)
                    if not pop_metadata or 'tags' not in pop_metadata or not pop_metadata['tags']:
                        continue

                    pop_tags = self._normalize_tags(pop_metadata['tags'])
                    if not pop_tags:
                        continue

                    # Вычисляем сходство по тегам
                    tag_similarity = 0.0
                    if game_tags and pop_tags:
                        common_tags = set(game_tags) & set(pop_tags)
                        if common_tags:
                            tag_similarity = len(common_tags) / max(len(game_tags), len(pop_tags))

                    # Получаем данные об игре
                    pop_game_data = self._get_game_data(pop_game_id)
                    if not pop_game_data:
                        continue

                    # Вычисляем итоговую оценку
                    positive_ratio = pop_game_data.get('positive_ratio', 0)
                    user_reviews = pop_game_data.get('user_reviews', 0)

                    # Добавляем случайность для разнообразия
                    randomness = np.random.uniform(0.9, 1.1)

                    score = (
                        tag_similarity * 0.5 +
                        (positive_ratio/100) * 0.3 +
                        (np.log1p(user_reviews) / 20) * 0.1 +
                        np.random.uniform(0, 0.1)  # Добавляем случайность
                    ) * randomness

                    popular_games_with_tags.append({
                        'app_id': int(pop_game_id),
                        'title': pop_game_data['title'],
                        'score': float(score),
                        'tag_similarity': float(tag_similarity),
                        'common_tags': list(set(game_tags) & set(pop_tags))
                    })

                    # Если нашли достаточно игр, останавливаемся
                    if len(popular_games_with_tags) >= top_n * 3:
                        break

                # Сортируем по оценке
                popular_games_with_tags.sort(key=lambda x: x['score'], reverse=True)

                # Комбинируем с рекомендациями по тегам
                combined_recommendations = []

                # Добавляем рекомендации по тегам
                for rec in content_recommendations:
                    if not any(p['app_id'] == rec['app_id'] for p in combined_recommendations):
                        combined_recommendations.append(rec)

                # Добавляем популярные игры с похожими тегами
                for rec in popular_games_with_tags:
                    if not any(p['app_id'] == rec['app_id'] for p in combined_recommendations):
                        combined_recommendations.append(rec)

                # Сортируем по оценке
                combined_recommendations.sort(key=lambda x: x['score'], reverse=True)

                # Берем только top_n рекомендаций
                top_recommendations = combined_recommendations[:top_n]

                return {
                    "input_title": game_title,
                    "matched_title": matched_title,
                    "recommendations": top_recommendations,
                    "method": "enhanced content-based"
                }

        except Exception as e:
            return {"error": f"Error generating recommendations: {str(e)}"}



    def get_available_tags(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Возвращает список всех доступных тегов

        Args:
            limit: Максимальное количество тегов для возврата

        Returns:
            Список словарей с тегами и их частотой
        """
        try:
            # Собираем все теги из метаданных
            all_tags = []
            for tags_list in self.metadata['tags']:
                if isinstance(tags_list, list):
                    all_tags.extend(tags_list)

            # Подсчитываем частоту каждого тега с использованием Counter
            from collections import Counter
            tag_counts = Counter(all_tags)

            # Сортируем по частоте и ограничиваем количество
            sorted_tags = tag_counts.most_common(limit)

            # Формируем результат
            return [{'tag': tag, 'count': count} for tag, count in sorted_tags]
        except Exception as e:
            return self._handle_error(e, "получении списка тегов")

    def get_available_years(self) -> List[Dict[str, Any]]:
        """
        Возвращает список всех доступных годов выпуска игр

        Returns:
            Список словарей с годами и количеством игр
        """
        try:
            # Преобразуем даты в годы
            games_with_years = self._convert_to_years(self.games)

            # Подсчитываем количество игр по годам
            year_counts = games_with_years['year'].value_counts().sort_index()

            # Фильтруем некорректные годы (None, слишком ранние или будущие)
            current_year = pd.Timestamp.now().year
            valid_years = year_counts[(year_counts.index >= 1980) & (year_counts.index <= current_year)]

            # Формируем результат
            return [{'year': int(year), 'count': int(count)} for year, count in valid_years.items()]
        except Exception as e:
            return self._handle_error(e, "получении списка годов")

    def get_available_ratings(self) -> List[Dict[str, Any]]:
        """
        Возвращает список всех доступных рейтингов

        Returns:
            Список словарей с рейтингами и количеством игр
        """
        try:
            # Подсчитываем количество игр по рейтингам
            rating_counts = self.games['rating'].value_counts()

            # Формируем результат
            return [{'rating': rating, 'count': int(count)} for rating, count in rating_counts.items() if pd.notna(rating)]
        except Exception as e:
            return self._handle_error(e, "получении списка рейтингов")

    def get_game_by_id(self, app_id: int) -> Optional[Dict[str, Any]]:
        """
        Возвращает информацию об игре по ее ID

        Args:
            app_id: ID игры

        Returns:
            Словарь с информацией об игре или None, если игра не найдена
        """
        try:
            # Получаем данные об игре
            game_data = self._get_game_data(app_id)
            if not game_data:
                return None

            # Получаем метаданные для игры
            metadata = self._get_game_metadata(app_id)

            # Преобразуем данные в словарь с правильными типами
            result = {}
            for column, value in game_data.items():
                # Преобразуем numpy типы в стандартные Python типы
                if pd.api.types.is_integer_dtype(type(value)) or isinstance(value, (np.int64, np.int32, np.int16, np.int8)):
                    result[column] = int(value)
                elif pd.api.types.is_float_dtype(type(value)) or isinstance(value, (np.float64, np.float32)):
                    result[column] = float(value)
                elif pd.api.types.is_bool_dtype(type(value)) or isinstance(value, np.bool_):
                    result[column] = bool(value)
                elif pd.api.types.is_datetime64_any_dtype(type(value)) or isinstance(value, pd.Timestamp):
                    result[column] = str(value)
                else:
                    result[column] = value

            # Добавляем метаданные, если они есть
            if metadata:
                result['description'] = metadata.get('description', '')
                result['tags'] = metadata.get('tags', [])
            else:
                result['description'] = ''
                result['tags'] = []

            # Добавляем информацию о том, есть ли игра в системе рекомендаций
            if app_id in self.game_to_idx:
                result['in_recommendation_system'] = True
                result['popularity_rank'] = self.game_to_idx[app_id]
            else:
                result['in_recommendation_system'] = False

            return result
        except Exception as e:
            print(f"Ошибка при получении информации об игре {app_id}: {str(e)}")
            return None

    def get_all_games(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Возвращает список всех игр с ограничением по количеству

        Args:
            limit: Максимальное количество игр для возврата

        Returns:
            Список словарей с информацией об играх
        """
        try:
            # Сначала выбираем игры, которые есть в системе рекомендаций
            recommended_games = self.games[self.games['app_id'].isin(self.popular_games)]

            # Сортируем по рейтингу и ограничиваем количество
            recommended_games = recommended_games.sort_values(by='positive_ratio', ascending=False).head(limit)

            # Форматируем результат
            result = []
            for _, game in recommended_games.iterrows():
                result.append(self._format_game_for_response(game.to_dict()))

            return result
        except Exception as e:
            return self._handle_error(e, "получении списка игр")

    def search_games_by_title(self, title: str) -> List[Dict[str, Any]]:
        """
        Поиск игр по названию

        Args:
            title: Строка для поиска в названии игры

        Returns:
            Список словарей с информацией о найденных играх
        """
        try:
            # Приводим к нижнему регистру для поиска
            title_lower = title.lower()

            # Ищем игры, в названии которых есть указанная строка
            results = self.games[self.games['title'].str.lower().str.contains(title_lower, na=False)]

            # Сортируем результаты: сначала игры в системе рекомендаций, затем по алфавиту
            results = results.sort_values(
                by=['title'],
                ascending=[True]
            )

            # Форматируем результат
            formatted_results = []
            for _, game in results.iterrows():
                game_dict = game.to_dict()
                # Добавляем информацию о том, есть ли игра в системе рекомендаций
                in_recommendation_system = game_dict['app_id'] in self.game_to_idx

                # Сортируем результаты так, чтобы игры в системе рекомендаций были первыми
                if in_recommendation_system:
                    formatted_results.insert(0, self._format_game_for_response(game_dict))
                else:
                    formatted_results.append(self._format_game_for_response(game_dict))

            return formatted_results
        except Exception as e:
            return self._handle_error(e, "поиске игр")

    def autocomplete_game_title(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Автокомплит названий игр по введенному запросу

        Args:
            query: Строка запроса для автокомплита
            limit: Максимальное количество результатов

        Returns:
            Список названий игр, подходящих для автокомплита
        """
        try:
            if not query or len(query) < 2:
                # Если запрос пустой или слишком короткий, возвращаем популярные игры
                return self._get_popular_games_for_autocomplete(limit)

            # Приводим к нижнему регистру для поиска
            query_lower = query.lower()

            # Ищем игры, названия которых начинаются с введенного запроса (более точное совпадение)
            starts_with_results = self.games[self.games['title'].str.lower().str.startswith(query_lower)]

            # Ищем игры, в названии которых содержится введенный запрос (менее точное совпадение)
            contains_results = self.games[
                ~self.games['app_id'].isin(starts_with_results['app_id']) &
                self.games['title'].str.lower().str.contains(query_lower, na=False)
            ]

            # Объединяем результаты, сначала точные совпадения, затем частичные
            combined_results = pd.concat([starts_with_results, contains_results])

            # Добавляем информацию о том, есть ли игра в системе рекомендаций
            combined_results['in_recommendation_system'] = combined_results['app_id'].apply(lambda x: x in self.game_to_idx)

            # Сортируем результаты: сначала игры в системе рекомендаций, затем по длине названия (более короткие названия вперед)
            combined_results['title_length'] = combined_results['title'].str.len()
            combined_results = combined_results.sort_values(
                by=['in_recommendation_system', 'title_length'],
                ascending=[False, True]
            ).head(limit)

            # Формируем результат
            return [
                {
                    'app_id': int(game['app_id']),
                    'title': game['title'],
                    'in_recommendation_system': bool(game['in_recommendation_system'])
                }
                for _, game in combined_results.iterrows()
            ]

        except Exception as e:
            return self._handle_error(e, "автокомплите названий игр")

    def _get_popular_games_for_autocomplete(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Получает список популярных игр для автокомплита

        Args:
            limit: Максимальное количество игр

        Returns:
            Список словарей с информацией о популярных играх
        """
        popular_games = self.games[self.games['app_id'].isin(self.popular_games)]
        popular_games = popular_games.sort_values(by='positive_ratio', ascending=False).head(limit)
        return [
            {
                'app_id': int(game['app_id']),
                'title': game['title'],
                'is_popular': True,
                'in_recommendation_system': True
            }
            for _, game in popular_games.iterrows()
        ]

    def recommend_by_tags(self, game_id: int, top_n: int = 10) -> List[Dict[str, Any]]:
        """
        Рекомендует игры на основе тегов указанной игры

        Args:
            game_id: ID игры
            top_n: Количество рекомендаций

        Returns:
            Список рекомендаций
        """
        try:
            # Получаем метаданные игры
            metadata = self._get_game_metadata(game_id)
            if not metadata or 'tags' not in metadata or not metadata['tags']:
                return self._get_random_recommendations(game_id, top_n)

            # Получаем теги игры и нормализуем их
            game_tags = self._normalize_tags(metadata['tags'])
            if not game_tags:
                return self._get_random_recommendations(game_id, top_n)

            # Ограничиваем количество тегов для ускорения поиска
            if len(game_tags) > 5:
                # Если тегов больше 5, берем только 5 самых популярных
                game_tags = game_tags[:5]
            game_tags_set = set(game_tags)

            # Фильтруем метаданные, чтобы включать только игры с тегами
            filtered_metadata = self.metadata[self.metadata['tags'].apply(lambda x: isinstance(x, list) and len(x) > 0)]

            # Находим игры с похожими тегами
            similar_games = []

            # Ограничиваем количество игр для сравнения
            sample_size = min(5000, len(filtered_metadata))
            metadata_sample = filtered_metadata.sample(n=sample_size) if len(filtered_metadata) > sample_size else filtered_metadata

            for _, meta in metadata_sample.iterrows():
                if meta['app_id'] == game_id:
                    continue

                # Получаем теги текущей игры и нормализуем их
                current_tags = self._normalize_tags(meta['tags'])
                current_tags_set = set(current_tags)

                # Находим общие теги
                common_tags = game_tags_set & current_tags_set

                if common_tags:
                    # Вычисляем оценку сходства (процент общих тегов)
                    similarity_score = len(common_tags) / max(len(game_tags_set), len(current_tags_set))

                    # Получаем данные об игре
                    game_data = self._get_game_data(meta['app_id'])
                    if not game_data:
                        continue

                    # Получаем дополнительные метрики для ранжирования
                    positive_ratio = game_data.get('positive_ratio', 0)
                    user_reviews = game_data.get('user_reviews', 0)

                    # Создаем составной скор для ранжирования
                    # Используем сходство тегов как основу, но добавляем влияние рейтинга и популярности
                    composite_score = similarity_score * 0.6 + (positive_ratio/100) * 0.3 + (np.log1p(user_reviews) / 20) * 0.1

                    similar_games.append({
                        'app_id': int(meta['app_id']),
                        'title': game_data['title'],
                        'score': float(similarity_score),
                        'common_tags': list(common_tags),
                        'composite_score': float(composite_score)
                    })

            # Сортируем игры по составному скору
            similar_games = sorted(similar_games, key=lambda x: x['composite_score'], reverse=True)

            # Ограничиваем количество рекомендаций
            return similar_games[:top_n]

        except Exception as e:
            return self._handle_error(e, f"получении рекомендаций по тегам для игры {game_id}")

    def _get_random_recommendations(self, exclude_game_id: int, count: int) -> List[Dict[str, Any]]:
        """
        Получает случайные рекомендации игр

        Args:
            exclude_game_id: ID игры, которую нужно исключить из рекомендаций
            count: Количество рекомендаций

        Returns:
            Список случайных рекомендаций
        """
        # Исключаем указанную игру
        filtered_games = self.games[self.games['app_id'] != exclude_game_id]

        # Выбираем случайные игры
        random_games = filtered_games.sample(min(count, len(filtered_games)))

        # Формируем результат
        recommendations = []
        for _, game in random_games.iterrows():
            recommendations.append({
                'app_id': int(game['app_id']),
                'title': game['title'],
                'score': 0.0,
                'common_tags': []
            })

        return recommendations

    def recommend_for_user_item_based(self, user_id: int, top_n: int = 10) -> Dict[str, Any]:
        """
        Рекомендует игры для конкретного пользователя на основе item-based коллаборативной фильтрации

        Args:
            user_id: ID пользователя
            top_n: Количество рекомендаций

        Returns:
            Словарь с рекомендациями игр для пользователя
        """
        try:
            # Проверяем, есть ли пользователь в системе рекомендаций
            if not self.has_recommendation_model:
                return {
                    "error": "Система рекомендаций не инициализирована",
                    "recommendations": []
                }

            # Получаем оценки пользователя из сбалансированной таблицы
            user_ratings = self.recommendations[self.recommendations['user_id'] == user_id]

            if user_ratings.empty:
                return {
                    "error": f"Пользователь с ID {user_id} не найден или у него нет оценок",
                    "recommendations": []
                }

            # Получаем список игр, которые пользователь уже оценил
            rated_games = set(user_ratings['app_id'].values)

            # Получаем положительные оценки пользователя
            positive_ratings = user_ratings[user_ratings['is_recommended'] == True]

            # Если у пользователя нет положительных оценок, рекомендуем популярные игры
            if positive_ratings.empty:
                print(f"Пользователь {user_id} не имеет положительных оценок, рекомендуем популярные игры")

                # Получаем популярные игры, исключая те, которые пользователь уже оценил
                popular_recommendations = self.get_popular_games(exclude_game_ids=rated_games, top_n=top_n)

                return {
                    "user_id": user_id,
                    "rated_games_count": len(rated_games),
                    "positive_ratings_count": 0,
                    "recommendation_type": "популярные",
                    "method": "популярные",
                    "message": "У вас нет положительных оценок. Показываем популярные игры, которые вы еще не оценили.",
                    "recommendations": popular_recommendations
                }

            # Получаем список игр с положительными оценками
            positive_game_ids = list(positive_ratings['app_id'].values)
            print(f"Пользователь {user_id} имеет {len(positive_game_ids)} положительно оцененных игр")

            # Ограничиваем количество игр для анализа, если их слишком много
            max_games_to_analyze = 20
            if len(positive_game_ids) > max_games_to_analyze:
                # Если игр слишком много, берем случайную выборку
                import random
                positive_game_ids = random.sample(positive_game_ids, max_games_to_analyze)
                print(f"Для анализа выбрано {len(positive_game_ids)} случайных игр")

            # Используем item-based коллаборативную фильтрацию
            # Для каждой положительно оцененной игры находим похожие игры
            item_based_recommendations = []

            for game_id in positive_game_ids:
                # Проверяем, есть ли игра в системе рекомендаций
                if game_id in self.game_to_idx:
                    # Получаем индекс игры в матрице
                    matrix_index = self.game_to_idx[game_id]

                    # Находим похожие игры с помощью косинусного сходства
                    # Используем матрицу игра-пользователь (транспонированную матрицу пользователь-игра)
                    game_vector = self.user_game_matrix_sparse[matrix_index].toarray().reshape(1, -1)

                    # Получаем больше соседей, чем нужно, для последующей фильтрации
                    extra_neighbors = min(50, len(self.popular_games) - 1)

                    # Находим ближайших соседей
                    distances, indices = self.knn.kneighbors(game_vector, n_neighbors=extra_neighbors + 1)

                    # Преобразуем в списки и удаляем первый элемент (это сама игра)
                    similar_indices = indices.squeeze().tolist()[1:]
                    similar_distances = distances.squeeze().tolist()[1:]

                    # Добавляем похожие игры в список рекомендаций
                    for idx, dist in zip(similar_indices, similar_distances):
                        similar_game_id = self.idx_to_game[idx]

                        # Пропускаем игры, которые пользователь уже оценил
                        if similar_game_id in rated_games:
                            continue

                        # Получаем данные об игре
                        game_data = self.games[self.games['app_id'] == similar_game_id]
                        if not game_data.empty:
                            game = game_data.iloc[0]

                            # Получаем метаданные игры, если они есть
                            metadata = self.metadata[self.metadata['app_id'] == similar_game_id]
                            tags = []
                            if not metadata.empty and 'tags' in metadata.columns:
                                tags = metadata.iloc[0]['tags'] if isinstance(metadata.iloc[0]['tags'], list) else []

                            # Вычисляем оценку сходства (1 - расстояние)
                            similarity_score = 1 - dist

                            # Добавляем игру в список рекомендаций
                            item_based_recommendations.append({
                                'app_id': int(similar_game_id),
                                'title': game['title'],
                                'rating': game['rating'] if 'rating' in game else None,
                                'positive_ratio': float(game['positive_ratio']) if 'positive_ratio' in game else None,
                                'score': float(similarity_score),
                                'source_game_id': int(game_id),
                                'tags': tags[:5]  # Ограничиваем количество тегов для краткости
                            })

            # Удаляем дубликаты, оставляя рекомендации с наивысшей оценкой
            unique_recommendations = {}
            for rec in item_based_recommendations:
                app_id = rec['app_id']
                if app_id not in unique_recommendations or rec['score'] > unique_recommendations[app_id]['score']:
                    unique_recommendations[app_id] = rec

            print(f"Получено {len(unique_recommendations)} уникальных рекомендаций на основе item-based фильтрации")

            # Сортируем рекомендации по оценке сходства
            sorted_recommendations = sorted(
                unique_recommendations.values(),
                key=lambda x: x['score'],
                reverse=True
            )

            # Ограничиваем количество рекомендаций
            top_recommendations = sorted_recommendations[:top_n]

            # Формируем результат
            recommendations = []
            for rec in top_recommendations:
                # Получаем данные об исходной игре (источнике рекомендации)
                source_game_data = self.games[self.games['app_id'] == rec['source_game_id']]
                source_game_title = source_game_data.iloc[0]['title'] if not source_game_data.empty else "Unknown"

                recommendations.append({
                    'app_id': rec['app_id'],
                    'title': rec['title'],
                    'rating': rec['rating'],
                    'positive_ratio': rec['positive_ratio'],
                    'score': rec['score'],
                    'source_game_id': rec['source_game_id'],
                    'source_game_title': source_game_title,
                    'tags': rec['tags']
                })

            print(f"Сформировано {len(recommendations)} рекомендаций")

            return {
                "user_id": user_id,
                "rated_games_count": len(rated_games),
                "positive_ratings_count": len(positive_ratings),
                "recommendation_type": "на основе похожих игр",
                "method": "на основе похожих игр",
                "message": "Рекомендации на основе игр, похожих на те, что вам понравились",
                "recommendations": recommendations
            }

        except Exception as e:
            print(f"Ошибка при получении рекомендаций на основе похожих игр для пользователя {user_id}: {str(e)}")
            return {
                "error": f"Ошибка при получении рекомендаций: {str(e)}",
                "user_id": user_id,
                "message": "Произошла ошибка при получении рекомендаций. Пожалуйста, попробуйте позже.",
                "recommendations": []
            }

    def get_popular_games(self, exclude_game_ids: Set[int] = None, top_n: int = 10) -> List[Dict[str, Any]]:
        """
        Получает список самых популярных игр, исключая указанные игры

        Args:
            exclude_game_ids: Множество ID игр, которые нужно исключить
            top_n: Количество игр для возврата

        Returns:
            Список словарей с информацией о популярных играх
        """
        try:
            if exclude_game_ids is None:
                exclude_game_ids = set()

            # Фильтруем игры, исключая те, которые пользователь уже оценил
            filtered_games = self.games[~self.games['app_id'].isin(exclude_game_ids)]

            # Сортируем игры по проценту положительных отзывов и количеству отзывов
            # Учитываем только игры с достаточным количеством отзывов (более 100)
            popular_games = filtered_games[filtered_games['user_reviews'] > 100].sort_values(
                by=['positive_ratio', 'user_reviews'],
                ascending=False
            ).head(top_n * 2)  # Берем в 2 раза больше игр, чтобы иметь запас

            # Формируем результат
            result = []
            for _, game in popular_games.iterrows():
                # Форматируем данные об игре для ответа
                game_info = self._format_game_for_response(game.to_dict(), include_tags=True)
                result.append(game_info)

                if len(result) >= top_n:
                    break

            return result

        except Exception as e:
            return self._handle_error(e, "получении популярных игр")

    def recommend_for_user(self, user_id: int, top_n: int = 10) -> Dict[str, Any]:
        """
        Рекомендует игры для конкретного пользователя на основе его оценок из сбалансированной таблицы

        Args:
            user_id: ID пользователя
            top_n: Количество рекомендаций

        Returns:
            Словарь с рекомендациями игр для пользователя
        """
        try:
            # Проверяем, есть ли пользователь в системе рекомендаций
            if not self.has_recommendation_model:
                return {
                    "error": "Система рекомендаций не инициализирована",
                    "recommendations": []
                }

            # Получаем оценки пользователя из сбалансированной таблицы
            user_ratings = self.recommendations[self.recommendations['user_id'] == user_id]

            if user_ratings.empty:
                return {
                    "error": f"Пользователь с ID {user_id} не найден или у него нет оценок",
                    "recommendations": []
                }

            # Получаем список игр, которые пользователь уже оценил
            rated_games = set(user_ratings['app_id'].values)

            # Получаем положительные оценки пользователя
            positive_ratings = user_ratings[user_ratings['is_recommended'] == True]

            # Если у пользователя нет положительных оценок, рекомендуем популярные игры
            if positive_ratings.empty:
                print(f"Пользователь {user_id} не имеет положительных оценок, рекомендуем популярные игры")

                # Получаем популярные игры, исключая те, которые пользователь уже оценил
                popular_recommendations = self.get_popular_games(exclude_game_ids=rated_games, top_n=top_n)

                return {
                    "user_id": user_id,
                    "rated_games_count": len(rated_games),
                    "positive_ratings_count": 0,
                    "recommendation_type": "popular",
                    "recommendations": popular_recommendations
                }

            # Получаем список игр с положительными оценками
            positive_game_ids = list(positive_ratings['app_id'].values)
            print(f"Пользователь {user_id} имеет {len(positive_game_ids)} положительно оцененных игр")

            # Ограничиваем количество игр для анализа, если их слишком много
            max_games_to_analyze = 20
            if len(positive_game_ids) > max_games_to_analyze:
                # Если игр слишком много, берем случайную выборку
                import random
                positive_game_ids = random.sample(positive_game_ids, max_games_to_analyze)
                print(f"Для анализа выбрано {len(positive_game_ids)} случайных игр")

            # Используем гибридный подход для рекомендаций
            # 1. Получаем рекомендации на основе тегов для каждой положительно оцененной игры
            content_based_recommendations = []
            for game_id in positive_game_ids:
                # Получаем рекомендации на основе тегов
                game_recommendations = self.recommend_by_tags(game_id, top_n=10)
                content_based_recommendations.extend(game_recommendations)

            # Удаляем дубликаты
            unique_recommendations = {}
            for rec in content_based_recommendations:
                app_id = rec['app_id']
                if app_id not in unique_recommendations and app_id not in rated_games:
                    unique_recommendations[app_id] = rec
                elif app_id in unique_recommendations and rec['score'] > unique_recommendations[app_id]['score']:
                    # Если игра уже есть в рекомендациях, но с более низкой оценкой, обновляем ее
                    unique_recommendations[app_id] = rec

            print(f"Получено {len(unique_recommendations)} уникальных рекомендаций на основе тегов")

            # Сортируем рекомендации по оценке сходства
            sorted_recommendations = sorted(
                unique_recommendations.values(),
                key=lambda x: x['score'],
                reverse=True
            )

            # Ограничиваем количество рекомендаций
            top_recommendations = sorted_recommendations[:top_n]

            # Формируем результат
            recommendations = []
            for rec in top_recommendations:
                game_id = rec['app_id']
                game_data = self.games[self.games['app_id'] == game_id]
                if not game_data.empty:
                    game = game_data.iloc[0]

                    # Получаем метаданные игры, если они есть
                    metadata = self.metadata[self.metadata['app_id'] == game_id]
                    tags = []
                    if not metadata.empty and 'tags' in metadata.columns:
                        tags = metadata.iloc[0]['tags'] if isinstance(metadata.iloc[0]['tags'], list) else []

                    recommendations.append({
                        'app_id': int(game_id),
                        'title': game['title'],
                        'rating': game['rating'] if 'rating' in game else None,
                        'positive_ratio': float(game['positive_ratio']) if 'positive_ratio' in game else None,
                        'score': float(rec['score']),
                        'common_tags': rec['common_tags'][:5],  # Ограничиваем количество общих тегов
                        'tags': tags[:5]  # Ограничиваем количество тегов для краткости
                    })

            print(f"Сформировано {len(recommendations)} рекомендаций")

            return {
                "user_id": user_id,
                "rated_games_count": len(rated_games),
                "positive_ratings_count": len(positive_ratings),
                "recommendation_type": "collaborative",
                "recommendations": recommendations
            }

        except Exception as e:
            print(f"Ошибка при получении рекомендаций для пользователя {user_id}: {str(e)}")
            return {
                "error": f"Ошибка при получении рекомендаций: {str(e)}",
                "user_id": user_id,
                "recommendations": []
            }

    def filter_games(
        self,
        tags: Optional[List[str]] = None,
        year_min: Optional[int] = None,
        year_max: Optional[int] = None,
        os: Optional[str] = None,
        rating: Optional[str] = None,
        min_positive_ratio: Optional[int] = None,
        steam_deck: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        Фильтрует игры по заданным критериям. Фильтры применяются последовательно.
        Если параметр равен None, то фильтрация по этому параметру не производится.

        Args:
            tags: Список тегов для фильтрации (все указанные теги должны присутствовать)
            year_min: Минимальный год выпуска
            year_max: Максимальный год выпуска
            os: Операционная система (win, mac, linux)
            rating: Рейтинг игры
            min_positive_ratio: Минимальный процент положительных отзывов
            steam_deck: Поддержка Steam Deck

        Returns:
            Словарь с информацией о результатах фильтрации
        """
        try:
            df = self.games.copy()
            meta = self.metadata.copy()

            # Список примененных фильтров
            applied_filters = []

            # Исходное количество игр
            initial_count = len(df)

            # Фильтр 1: по тегам
            if tags and len(tags) > 0:
                # Нормализуем теги
                search_tags = self._normalize_tags(tags)

                if search_tags:
                    # Преобразуем теги в нижний регистр для поиска
                    meta['tags_lower'] = meta['tags'].apply(lambda t: self._normalize_tags(t))

                    # Фильтруем игры по тегам (все указанные теги должны присутствовать)
                    filtered_meta = meta[meta['tags_lower'].apply(lambda t: all(search_tag in t for search_tag in search_tags))]
                    df = df[df['app_id'].isin(filtered_meta['app_id'])]
                    applied_filters.append({
                        "name": "Теги",
                        "value": ", ".join(tags),
                        "count_before": initial_count,
                        "count_after": len(df)
                    })

            # Преобразуем даты в годы для фильтрации по годам
            df = self._convert_to_years(df)

            # Фильтр 2: по минимальному году
            if year_min is not None:
                count_before = len(df)
                df = df[df['year'] >= year_min]
                applied_filters.append({
                    "name": "Минимальный год",
                    "value": str(year_min),
                    "count_before": count_before,
                    "count_after": len(df)
                })

            # Фильтр 3: по максимальному году
            if year_max is not None:
                count_before = len(df)
                df = df[df['year'] <= year_max]
                applied_filters.append({
                    "name": "Максимальный год",
                    "value": str(year_max),
                    "count_before": count_before,
                    "count_after": len(df)
                })

            # Фильтр 4: по ОС
            if os is not None and os.strip():
                count_before = len(df)
                os_lower = os.lower().strip()
                if os_lower in ['win', 'mac', 'linux']:
                    df = df[df[os_lower] == True]
                    applied_filters.append({
                        "name": "Операционная система",
                        "value": os,
                        "count_before": count_before,
                        "count_after": len(df)
                    })

            # Фильтр 5: по рейтингу
            if rating is not None and rating.strip():
                count_before = len(df)
                df = df[df['rating'].str.lower() == rating.lower().strip()]
                applied_filters.append({
                    "name": "Рейтинг",
                    "value": rating,
                    "count_before": count_before,
                    "count_after": len(df)
                })

            # Фильтр 6: по минимальному проценту положительных отзывов
            if min_positive_ratio is not None:
                count_before = len(df)
                df = df[df['positive_ratio'] >= min_positive_ratio]
                applied_filters.append({
                    "name": "Минимальный процент положительных отзывов",
                    "value": f"{min_positive_ratio}%",
                    "count_before": count_before,
                    "count_after": len(df)
                })

            # Фильтр 7: по поддержке Steam Deck
            if steam_deck is not None:
                count_before = len(df)
                df = df[df['steam_deck'] == steam_deck]
                applied_filters.append({
                    "name": "Steam Deck",
                    "value": "Да" if steam_deck else "Нет",
                    "count_before": count_before,
                    "count_after": len(df)
                })

            # Преобразуем результаты в формат JSON
            results = df[['app_id', 'title', 'rating', 'positive_ratio']].drop_duplicates()

            # Добавляем информацию о том, есть ли игра в системе рекомендаций
            results['in_recommendation_system'] = results['app_id'].apply(lambda x: x in self.game_to_idx)

            # Формируем итоговый результат
            return {
                "total": len(results),
                "initial_count": initial_count,
                "applied_filters": applied_filters,
                "games": results.to_dict(orient='records')
            }
        except Exception as e:
            return self._handle_error(e, "фильтрации игр")

    def get_user_ratings(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Получает оценки пользователя

        Args:
            user_id: ID пользователя

        Returns:
            Список словарей с оценками пользователя
        """
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Получаем оценки пользователя
            cursor.execute("""
                SELECT app_id, rating FROM user_ratings
                WHERE user_id = ?
            """, (user_id,))

            ratings = []
            for row in cursor.fetchall():
                app_id, rating = row

                # Получаем название игры из DataFrame
                game_data = self.games[self.games['app_id'] == app_id]
                title = game_data['title'].iloc[0] if not game_data.empty else f"Game {app_id}"

                ratings.append({
                    'app_id': app_id,
                    'rating': rating,
                    'title': title
                })

            conn.close()
            return ratings
        except Exception as e:
            return self._handle_error(e, f"получении оценок пользователя {user_id}")

    def convert_user_ratings_to_balanced_recommendations(self, user_id: int) -> pd.DataFrame:
        """
        Конвертирует оценки пользователя из таблицы user_ratings в формат сбалансированных рекомендаций

        Args:
            user_id: ID пользователя

        Returns:
            DataFrame с оценками в формате сбалансированных рекомендаций
        """
        try:
            # Получаем оценки пользователя
            user_ratings = self.get_user_ratings(user_id)

            if isinstance(user_ratings, dict) and "error" in user_ratings:
                print(f"Ошибка при получении оценок пользователя: {user_ratings['error']}")
                return pd.DataFrame()

            if not user_ratings:
                print(f"У пользователя {user_id} нет оценок")
                return pd.DataFrame()

            # Создаем DataFrame в формате сбалансированных рекомендаций
            balanced_recommendations = []

            for rating in user_ratings:
                app_id = rating['app_id']
                # Преобразуем оценку от 1-5 в булево значение
                # 1-2: отрицательная оценка (False), 3-5: положительная оценка (True)
                is_recommended = rating['rating'] >= 3

                balanced_recommendations.append({
                    'app_id': app_id,
                    'user_id': user_id,
                    'review_id': f"user_{user_id}_game_{app_id}",
                    'is_recommended': is_recommended,
                    'helpful': 1,  # Устанавливаем значение по умолчанию
                    'funny': 0,    # Устанавливаем значение по умолчанию
                    'date': pd.Timestamp.now(),
                    'hours': 10    # Устанавливаем значение по умолчанию
                })

            return pd.DataFrame(balanced_recommendations)
        except Exception as e:
            print(f"Ошибка при конвертации оценок пользователя {user_id}: {str(e)}")
            return pd.DataFrame()

    def add_user_rating(self, user_id: int, app_id: int, rating: int) -> Dict[str, Any]:
        """
        Добавляет или обновляет оценку пользователя для игры

        Args:
            user_id: ID пользователя
            app_id: ID игры
            rating: Оценка (от 1 до 5)

        Returns:
            Словарь с результатом операции
        """
        try:
            # Проверяем, что оценка в допустимом диапазоне
            if rating < 1 or rating > 5:
                return {"error": "Оценка должна быть от 1 до 5"}

            # Проверяем, что игра существует
            game_data = self._get_game_data(app_id)
            if not game_data:
                return {"error": f"Игра с ID {app_id} не найдена"}

            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Проверяем, существует ли уже оценка
            cursor.execute("SELECT id FROM user_ratings WHERE user_id = ? AND app_id = ?", (user_id, app_id))
            existing_rating = cursor.fetchone()

            if existing_rating:
                # Обновляем существующую оценку
                cursor.execute("""
                    UPDATE user_ratings
                    SET rating = ?, timestamp = CURRENT_TIMESTAMP
                    WHERE user_id = ? AND app_id = ?
                """, (rating, user_id, app_id))
                message = "Оценка успешно обновлена"
            else:
                # Добавляем новую оценку
                cursor.execute("""
                    INSERT INTO user_ratings (user_id, app_id, rating)
                    VALUES (?, ?, ?)
                """, (user_id, app_id, rating))
                message = "Оценка успешно добавлена"

            conn.commit()
            conn.close()

            return {
                "success": True,
                "message": message,
                "user_id": user_id,
                "app_id": app_id,
                "rating": rating,
                "title": game_data['title']
            }
        except Exception as e:
            return self._handle_error(e, f"добавлении оценки пользователя {user_id} для игры {app_id}")

    def delete_user_rating(self, user_id: int, app_id: int) -> Dict[str, Any]:
        """
        Удаляет оценку пользователя для игры

        Args:
            user_id: ID пользователя
            app_id: ID игры

        Returns:
            Словарь с результатом операции
        """
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Проверяем, существует ли оценка
            cursor.execute("SELECT id FROM user_ratings WHERE user_id = ? AND app_id = ?", (user_id, app_id))
            existing_rating = cursor.fetchone()

            if not existing_rating:
                conn.close()
                return {"error": f"Оценка для игры {app_id} от пользователя {user_id} не найдена"}

            # Удаляем оценку
            cursor.execute("DELETE FROM user_ratings WHERE user_id = ? AND app_id = ?", (user_id, app_id))
            conn.commit()
            conn.close()

            return {
                "success": True,
                "message": "Оценка успешно удалена",
                "user_id": user_id,
                "app_id": app_id
            }
        except Exception as e:
            return self._handle_error(e, f"удалении оценки пользователя {user_id} для игры {app_id}")

    def get_popular_games(self, exclude_game_ids: Set[int] = None, top_n: int = 10) -> List[Dict[str, Any]]:
        """
        Возвращает список популярных игр

        Args:
            exclude_game_ids: Множество ID игр, которые нужно исключить из результатов
            top_n: Количество игр для возврата

        Returns:
            Список словарей с информацией о популярных играх
        """
        try:
            # Если множество исключений не задано, создаем пустое
            if exclude_game_ids is None:
                exclude_game_ids = set()

            # Сортируем игры по количеству отзывов и проценту положительных отзывов
            popular_games = self.games.sort_values(
                by=['user_reviews', 'positive_ratio'],
                ascending=[False, False]
            )

            # Фильтруем игры, исключая те, которые нужно исключить
            popular_games = popular_games[~popular_games['app_id'].isin(exclude_game_ids)]

            # Берем top_n игр
            popular_games = popular_games.head(top_n)

            # Формируем результат
            result = []
            for _, game in popular_games.iterrows():
                result.append({
                    'app_id': int(game['app_id']),
                    'title': game['title'],
                    'score': float(game['positive_ratio']) / 100 if pd.notna(game['positive_ratio']) else 0.5,
                    'method': 'popular'
                })

            return result
        except Exception as e:
            print(f"Ошибка при получении популярных игр: {str(e)}")
            return []

    def recommend_for_user(self, user_id: int, top_n: int = 10) -> Dict[str, Any]:
        """
        Рекомендует игры для пользователя на основе его оценок, используя существующие методы рекомендаций

        Args:
            user_id: ID пользователя
            top_n: Количество рекомендаций

        Returns:
            Словарь с рекомендациями
        """
        try:
            # Получаем оценки пользователя
            user_ratings_result = self.get_user_ratings(user_id)

            # Проверяем, не вернулась ли ошибка
            if isinstance(user_ratings_result, dict) and "error" in user_ratings_result:
                # Если у пользователя нет оценок, возвращаем популярные игры
                return {
                    "user_id": user_id,
                    "recommendations": self.get_popular_games(top_n=top_n),
                    "method": "популярные (нет оценок)",
                    "message": "У вас пока нет оценок. Показываем популярные игры."
                }

            user_ratings = user_ratings_result

            if not user_ratings:
                # Если у пользователя нет оценок, возвращаем популярные игры
                return {
                    "user_id": user_id,
                    "recommendations": self.get_popular_games(top_n=top_n),
                    "method": "популярные (нет оценок)",
                    "message": "У вас пока нет оценок. Показываем популярные игры."
                }

            # Конвертируем оценки пользователя в формат сбалансированных рекомендаций
            balanced_recommendations = self.convert_user_ratings_to_balanced_recommendations(user_id)

            if balanced_recommendations.empty:
                # Если не удалось конвертировать оценки, возвращаем популярные игры
                return {
                    "user_id": user_id,
                    "recommendations": self.get_popular_games(top_n=top_n),
                    "method": "популярные (ошибка конвертации)",
                    "message": "Ошибка при обработке ваших оценок. Показываем популярные игры."
                }

            # Получаем список игр, которые пользователь уже оценил
            rated_games = set([rating['app_id'] for rating in user_ratings])

            # Проверяем, есть ли у пользователя положительные оценки
            positive_ratings = balanced_recommendations[balanced_recommendations['is_recommended'] == True]

            if positive_ratings.empty:
                # Если у пользователя нет положительных оценок, возвращаем популярные игры
                return {
                    "user_id": user_id,
                    "recommendations": self.get_popular_games(exclude_game_ids=rated_games, top_n=top_n),
                    "method": "популярные (нет положительных оценок)",
                    "message": "У вас нет положительных оценок. Показываем популярные игры, которые вы еще не оценили."
                }

            # Временно сохраняем оригинальные рекомендации
            original_recommendations = self.recommendations.copy()

            try:
                # Добавляем оценки пользователя к общим рекомендациям
                self.recommendations = pd.concat([self.recommendations, balanced_recommendations])

                # Используем существующий метод item-based коллаборативной фильтрации
                item_based_recommendations = self.recommend_for_user_item_based(user_id, top_n=top_n)

                # Проверяем, есть ли рекомендации
                if "error" in item_based_recommendations or not item_based_recommendations.get("recommendations"):
                    # Если не удалось получить рекомендации через item-based, используем тег-бейсд подход

                    # Получаем список игр с положительными оценками
                    positive_game_ids = list(positive_ratings['app_id'].values)

                    # Ограничиваем количество игр для анализа
                    max_games_to_analyze = 5
                    if len(positive_game_ids) > max_games_to_analyze:
                        import random
                        positive_game_ids = random.sample(positive_game_ids, max_games_to_analyze)

                    # Получаем рекомендации на основе тегов для каждой положительно оцененной игры
                    tag_based_recommendations = []
                    for game_id in positive_game_ids:
                        game_recommendations = self.recommend_by_tags(game_id, top_n=top_n)
                        tag_based_recommendations.extend(game_recommendations)

                    # Удаляем дубликаты и игры, которые пользователь уже оценил
                    unique_recommendations = {}
                    for rec in tag_based_recommendations:
                        app_id = rec['app_id']
                        if app_id not in unique_recommendations and app_id not in rated_games:
                            unique_recommendations[app_id] = rec
                        elif app_id in unique_recommendations and rec['score'] > unique_recommendations[app_id]['score']:
                            unique_recommendations[app_id] = rec

                    # Сортируем рекомендации по оценке
                    sorted_recommendations = sorted(
                        unique_recommendations.values(),
                        key=lambda x: x['score'],
                        reverse=True
                    )

                    # Ограничиваем количество рекомендаций
                    top_recommendations = sorted_recommendations[:top_n]

                    return {
                        "user_id": user_id,
                        "recommendations": top_recommendations,
                        "method": "на основе тегов",
                        "rated_games_count": len(rated_games),
                        "positive_ratings_count": len(positive_ratings),
                        "message": "Рекомендации на основе тегов игр, которые вам понравились"
                    }

                # Обновляем метод в ответе на русский
                if item_based_recommendations.get("method") == "item-based":
                    item_based_recommendations["method"] = "на основе похожих игр"
                    item_based_recommendations["message"] = "Рекомендации на основе игр, похожих на те, что вам понравились"

                return item_based_recommendations

            finally:
                # Восстанавливаем оригинальные рекомендации
                self.recommendations = original_recommendations

        except Exception as e:
            return self._handle_error(e, f"получении рекомендаций для пользователя {user_id}")
