# Source Code Directory

Основной исходный код системы рекомендаций игр.

## Структура

### Основные модули
- `main.py` - Точка входа FastAPI приложения
- `database.py` - Настройка подключения к базе данных
- `auth.py` - Система аутентификации и авторизации
- `game_recommender_db.py` - Основная логика рекомендательной системы
- `game_lists.py` - Управление списками игр пользователей
- `user_ratings.py` - Управление оценками игр пользователями
- `migrate_db.py` - Утилиты для миграции базы данных

### Папки
- `api/` - REST API эндпоинты
- `models/` - Модели данных SQLModel
- `cache/` - Кэшированные данные для быстрого доступа

### API эндпоинты
- `api/endpoints.py` - Основные эндпоинты (игры, рекомендации, поиск)
- `api/auth_endpoints.py` - Эндпоинты аутентификации и пользователей
- `api/steamapi.py` - Интеграция с Steam API

### Модели данных
- `models/models.py` - SQLModel модели для всех таблиц базы данных

### Кэш
- `cache/` - Сериализованные данные для ускорения работы системы рекомендаций

## Запуск

Для запуска приложения:
```bash
python src/main.py
```

Или через корневой скрипт:
```bash
python run.py
```
