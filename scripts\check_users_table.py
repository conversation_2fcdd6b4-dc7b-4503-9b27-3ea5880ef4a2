"""
Проверка структуры таблицы users
"""
import os
import sqlite3

def main():
    """Основная функция проверки таблицы users"""
    # Ищем все файлы .db в текущей директории и поддиректориях
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_path = os.path.join(root, file)
                db_files.append(db_path)
    
    if not db_files:
        print("Файлы баз данных SQLite не найдены")
        return
    
    print(f"Найдено {len(db_files)} файлов баз данных SQLite:")
    for i, db_path in enumerate(db_files, 1):
        print(f"{i}. {db_path}")
        check_users_table(db_path)
        print()

def check_users_table(db_path):
    """Проверка таблицы users"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем, существует ли таблица users
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print(f"   Таблица users не найдена в базе данных {db_path}")
            conn.close()
            return
        
        # Получаем структуру таблицы users
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        print(f"   Структура таблицы users в базе данных {db_path}:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]}) {'PRIMARY KEY' if col[5] else ''}")
        
        conn.close()
    except Exception as e:
        print(f"   Ошибка при проверке таблицы users: {str(e)}")

if __name__ == "__main__":
    main()
