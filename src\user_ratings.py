from typing import List, Dict, Any, Optional
from sqlmodel import Session, select
from src.models.models import UserRating, Game

def add_user_rating(user_id: int, app_id: int, rating: int, session: Session) -> Dict[str, Any]:
    """
    Добавляет или обновляет оценку пользователя для игры

    Args:
        user_id: ID пользователя
        app_id: ID игры
        rating: Оценка (от 1 до 5)
        session: Сессия базы данных

    Returns:
        Словарь с результатом операции
    """
    try:
        # Проверяем, что оценка в допустимом диапазоне
        if rating < 1 or rating > 5:
            return {"error": "Оценка должна быть от 1 до 5"}

        # Проверяем, что игра существует
        game = session.exec(select(Game).where(Game.app_id == app_id)).first()
        if not game:
            return {"error": f"Игра с ID {app_id} не найдена"}

        # Проверяем, существует ли уже оценка
        existing_rating = session.exec(
            select(UserRating).where(
                (UserRating.user_id == user_id) & 
                (UserRating.app_id == app_id)
            )
        ).first()

        if existing_rating:
            # Обновляем существующую оценку
            existing_rating.rating = rating
            session.add(existing_rating)
            session.commit()
            message = "Оценка успешно обновлена"
        else:
            # Добавляем новую оценку
            new_rating = UserRating(user_id=user_id, app_id=app_id, rating=rating)
            session.add(new_rating)
            session.commit()
            message = "Оценка успешно добавлена"

        return {
            "success": True,
            "message": message,
            "user_id": user_id,
            "app_id": app_id,
            "rating": rating,
            "title": game.title
        }
    except Exception as e:
        return {"error": f"Ошибка при добавлении оценки: {str(e)}"}

def delete_user_rating(user_id: int, app_id: int, session: Session) -> Dict[str, Any]:
    """
    Удаляет оценку пользователя для игры

    Args:
        user_id: ID пользователя
        app_id: ID игры
        session: Сессия базы данных

    Returns:
        Словарь с результатом операции
    """
    try:
        # Проверяем, существует ли оценка
        rating = session.exec(
            select(UserRating).where(
                (UserRating.user_id == user_id) & 
                (UserRating.app_id == app_id)
            )
        ).first()

        if not rating:
            return {"error": f"Оценка для игры {app_id} от пользователя {user_id} не найдена"}

        # Удаляем оценку
        session.delete(rating)
        session.commit()

        return {
            "success": True,
            "message": "Оценка успешно удалена",
            "user_id": user_id,
            "app_id": app_id
        }
    except Exception as e:
        return {"error": f"Ошибка при удалении оценки: {str(e)}"}

def get_user_ratings(user_id: int, session: Session) -> List[Dict[str, Any]]:
    """
    Получает все оценки пользователя

    Args:
        user_id: ID пользователя
        session: Сессия базы данных

    Returns:
        Список словарей с оценками пользователя
    """
    try:
        # Получаем оценки пользователя из базы данных
        ratings_query = select(UserRating, Game).join(Game).where(UserRating.user_id == user_id)
        ratings_result = session.exec(ratings_query).all()

        # Форматируем результат
        ratings = [
            {
                "app_id": game.app_id,
                "rating": rating.rating,
                "title": game.title,
                "timestamp": rating.timestamp.isoformat()
            }
            for rating, game in ratings_result
        ]

        return ratings
    except Exception as e:
        print(f"Ошибка при получении оценок пользователя {user_id}: {str(e)}")
        return []

def get_game_average_rating(app_id: int, session: Session) -> Dict[str, Any]:
    """
    Получает среднюю оценку игры

    Args:
        app_id: ID игры
        session: Сессия базы данных

    Returns:
        Словарь со средней оценкой игры
    """
    try:
        # Проверяем, что игра существует
        game = session.exec(select(Game).where(Game.app_id == app_id)).first()
        if not game:
            return {"error": f"Игра с ID {app_id} не найдена"}

        # Получаем все оценки игры
        ratings_query = select(UserRating).where(UserRating.app_id == app_id)
        ratings = session.exec(ratings_query).all()

        if not ratings:
            return {
                "app_id": app_id,
                "title": game.title,
                "average_rating": None,
                "ratings_count": 0
            }

        # Вычисляем среднюю оценку
        total_rating = sum(rating.rating for rating in ratings)
        average_rating = total_rating / len(ratings)

        return {
            "app_id": app_id,
            "title": game.title,
            "average_rating": round(average_rating, 1),
            "ratings_count": len(ratings)
        }
    except Exception as e:
        return {"error": f"Ошибка при получении средней оценки игры: {str(e)}"}

def get_top_rated_games(limit: int, min_ratings: int, session: Session) -> List[Dict[str, Any]]:
    """
    Получает список игр с наивысшими оценками

    Args:
        limit: Максимальное количество игр для возврата
        min_ratings: Минимальное количество оценок для включения игры в список
        session: Сессия базы данных

    Returns:
        Список словарей с информацией о лучших играх
    """
    try:
        # Получаем все оценки
        ratings_query = select(UserRating, Game).join(Game)
        ratings_result = session.exec(ratings_query).all()

        # Группируем оценки по играм
        games_ratings = {}
        for rating, game in ratings_result:
            if game.app_id not in games_ratings:
                games_ratings[game.app_id] = {
                    "app_id": game.app_id,
                    "title": game.title,
                    "ratings": [],
                    "average_rating": 0,
                    "ratings_count": 0
                }
            games_ratings[game.app_id]["ratings"].append(rating.rating)

        # Вычисляем средние оценки и фильтруем игры с недостаточным количеством оценок
        top_games = []
        for game_id, game_data in games_ratings.items():
            ratings_count = len(game_data["ratings"])
            if ratings_count >= min_ratings:
                average_rating = sum(game_data["ratings"]) / ratings_count
                top_games.append({
                    "app_id": game_data["app_id"],
                    "title": game_data["title"],
                    "average_rating": round(average_rating, 1),
                    "ratings_count": ratings_count
                })

        # Сортируем игры по средней оценке (по убыванию)
        top_games.sort(key=lambda x: x["average_rating"], reverse=True)

        # Ограничиваем количество возвращаемых игр
        return top_games[:limit]
    except Exception as e:
        print(f"Ошибка при получении лучших игр: {str(e)}")
        return []
