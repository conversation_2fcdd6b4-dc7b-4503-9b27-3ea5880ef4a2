"""
Скрипт для проверки структуры таблицы users
"""

import sys
import os

# Добавляем корневую директорию в путь для импорта модулей
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlmodel import Session, text
from src.database import engine

def check_users_table_structure():
    """Проверяет структуру таблицы users"""
    print("🔍 Проверка структуры таблицы users")
    print("=" * 40)
    
    try:
        with Session(engine) as session:
            # Получаем информацию о структуре таблицы users
            result = session.exec(text("PRAGMA table_info(users)"))
            columns = result.fetchall()
            
            if not columns:
                print("❌ Таблица users не найдена!")
                return False
            
            print("📋 Структура таблицы users:")
            print(f"{'№':<3} {'Название':<20} {'Тип':<15} {'NULL':<5} {'По умолчанию':<15} {'PK':<3}")
            print("-" * 70)
            
            has_avatar = False
            has_avatar_content_type = False
            
            for column in columns:
                cid, name, type_, notnull, default_value, pk = column
                null_str = "NO" if notnull else "YES"
                pk_str = "YES" if pk else "NO"
                default_str = str(default_value) if default_value is not None else ""
                
                print(f"{cid:<3} {name:<20} {type_:<15} {null_str:<5} {default_str:<15} {pk_str:<3}")
                
                if name == "avatar":
                    has_avatar = True
                if name == "avatar_content_type":
                    has_avatar_content_type = True
            
            print()
            print("✅ Проверка полей аватара:")
            print(f"   avatar: {'✅ Найдено' if has_avatar else '❌ Отсутствует'}")
            print(f"   avatar_content_type: {'✅ Найдено' if has_avatar_content_type else '❌ Отсутствует'}")
            
            if has_avatar and has_avatar_content_type:
                print("\n🎉 Все поля аватара присутствуют в таблице!")
                return True
            else:
                print("\n⚠️  Некоторые поля аватара отсутствуют!")
                return False
                
    except Exception as e:
        print(f"❌ Ошибка при проверке таблицы: {str(e)}")
        return False

def main():
    """Основная функция"""
    success = check_users_table_structure()
    
    if success:
        print("\n✅ Структура таблицы users корректна!")
        return 0
    else:
        print("\n❌ Проблемы со структурой таблицы users!")
        print("💡 Попробуйте применить миграции: python -m alembic upgrade head")
        return 1

if __name__ == "__main__":
    sys.exit(main())
