# Система рекомендаций игр Steam

Проект представляет собой полноценную рекомендательную систему для игр Steam с гибридным методом рекомендаций, веб-интерфейсом и системой пользователей.

## Особенности

- **Гибридный метод рекомендаций**: Комбинация коллаборативной фильтрации, контент-бейсд фильтрации и учета рейтинга и популярности игр
- **Система пользователей**: Регистрация, аутентификация, загрузка аватаров
- **Персональные списки игр**: Пользователи могут создавать списки "играю", "планирую", "завершил"
- **Оценки игр**: Пользователи могут оценивать игры от 1 до 5 звезд
- **Интеграция с Steam API**: Получение изображений игр из Steam
- **Асинхронный API**: Построен на FastAPI для высокой производительности
- **Веб-интерфейс**: Простой интерфейс для взаимодействия с системой рекомендаций
- **Автокомплит**: Функция автокомплита при вводе названия игры
- **Фильтрация игр**: Возможность фильтрации игр по тегам, году выпуска, рейтингу и другим параметрам
- **Документация API**: Автоматически генерируемая документация Swagger
- **SVD-модель**: Использование SVD (Singular Value Decomposition) для уменьшения разреженности матрицы рекомендаций

## Структура проекта

```
Game-Recommendation-System/
├── src/                    # Основной исходный код
│   ├── api/                # API эндпоинты
│   │   ├── __init__.py     # Инициализация API модуля
│   │   ├── endpoints.py    # Основные эндпоинты (игры, рекомендации)
│   │   ├── auth_endpoints.py # Эндпоинты аутентификации и пользователей
│   │   └── steamapi.py     # Интеграция с Steam API
│   ├── models/             # Модели данных
│   │   └── models.py       # SQLModel модели для базы данных
│   ├── cache/              # Кэш данных для быстрого доступа
│   ├── auth.py             # Система аутентификации
│   ├── database.py         # Настройка и подключение к базе данных
│   ├── game_recommender_db.py # Основной класс рекомендательной системы
│   ├── game_lists.py       # Работа со списками игр пользователей
│   ├── user_ratings.py     # Работа с оценками пользователей
│   ├── main.py             # Основной файл FastAPI приложения
│   └── migrate_db.py       # Миграция базы данных
├── static/                 # Статические файлы для фронтенда
│   ├── api-client.js       # JavaScript клиент для API
│   ├── index.html          # Главная страница
│   └── filter.html         # Страница фильтрации игр
├── data/                   # Файлы данных
│   └── games.db            # База данных игр
├── migrations/             # Миграции базы данных Alembic
├── scripts/                # Утилиты и скрипты для обслуживания
├── tests/                  # Комплексная система тестирования
├── archive/                # Архивные файлы и старые версии
├── requirements.txt        # Зависимости Python
├── run.py                  # Файл для запуска приложения
└── README.md               # Документация проекта
```

## Установка и запуск

1. Клонируйте репозиторий
2. Установите зависимости:
   ```bash
   pip install -r requirements.txt
   ```
3. Запустите приложение:
   ```bash
   python run.py
   ```
4. Откройте веб-браузер и перейдите по адресу:
   ```
   http://127.0.0.1:8000/
   ```

## Быстрый старт

1. **Регистрация пользователя**: Перейдите на `/docs` и используйте эндпоинт `POST /auth/register`
2. **Получение токена**: Используйте `POST /auth/token` для входа в систему
3. **Поиск игр**: Используйте веб-интерфейс или API для поиска игр
4. **Получение рекомендаций**: Попробуйте эндпоинт `GET /recommend/{title}` или персональные рекомендации
5. **Управление списками**: Добавляйте игры в свои списки через API

## Тестирование

Система включает комплексную систему тестирования для проверки всех функций:

### Запуск всех тестов
```bash
python tests/run_tests.py
```

### Только функциональные тесты
```bash
python tests/run_tests.py --comprehensive
```

### Только тесты производительности
```bash
python tests/run_tests.py --performance
```

### Быстрая проверка
```bash
python tests/run_tests.py --quick
```

**Требования для тестирования:**
- Запущенное приложение (`python run.py`)
- Доступная база данных
- Установленные зависимости

Подробная документация по тестированию: [tests/README.md](tests/README.md)

## API эндпоинты

### Игры и рекомендации
- `GET /recommend/{title}` - Получение рекомендаций для игры по названию
- `GET /games` - Получение списка всех игр
- `GET /games/{app_id}` - Получение полной информации об игре по ID (включая изображение из Steam)
- `GET /games/search/{title}` - Поиск игр по названию
- `GET /games/autocomplete` - Автокомплит названий игр
- `GET /games/filter` - Фильтрация игр по различным параметрам
- `GET /games/filter/tags` - Получение списка доступных тегов
- `GET /games/filter/years` - Получение списка доступных годов выпуска
- `GET /games/filter/ratings` - Получение списка доступных рейтингов
- `GET /games/filter/os` - Получение списка доступных операционных систем
- `GET /get_game_image/{app_id}` - Получение изображения игры из Steam API

### Аутентификация и пользователи
- `POST /auth/register` - Регистрация нового пользователя
- `POST /auth/token` - Получение токена доступа (вход в систему)
- `GET /auth/users/me` - Получение информации о текущем пользователе
- `GET /auth/users/me/recommendations` - Персональные рекомендации для пользователя
- `POST /auth/users/me/avatar` - Загрузка аватара пользователя
- `GET /auth/users/{user_id}/avatar` - Получение аватара пользователя
- `DELETE /auth/users/me/avatar` - Удаление аватара пользователя

### Оценки игр
- `POST /auth/users/me/ratings` - Добавление/обновление оценки игры
- `DELETE /auth/users/me/ratings/{app_id}` - Удаление оценки игры
- `GET /auth/users/me/ratings` - Получение всех оценок пользователя

### Списки игр
- `POST /auth/users/me/game-lists/{list_type}/{app_id}` - Добавление игры в список
- `DELETE /auth/users/me/game-lists/{app_id}` - Удаление игры из списков
- `GET /auth/users/me/game-lists` - Получение всех списков игр пользователя

## Гибридный метод рекомендаций

Система использует гибридный метод рекомендаций, который комбинирует:

1. **Коллаборативную фильтрацию**: Рекомендации на основе похожести игр по оценкам пользователей. Использует алгоритм k-ближайших соседей (KNN) для нахождения похожих игр.

2. **Контент-бейсд фильтрацию**: Рекомендации на основе похожести тегов игр. Вычисляет оценку похожести как отношение количества общих тегов к общему количеству уникальных тегов.

3. **Рейтинг и популярность**: Учитывает процент положительных отзывов и количество отзывов для ранжирования рекомендаций.

Гибридная оценка вычисляется по формуле:
- 40% коллаборативная оценка
- 40% контент-бейсд оценка
- 15% рейтинг (процент положительных отзывов)
- 5% популярность (количество отзывов)

## Подключение фронтенда с Vue.js

Для подключения фронтенда с Vue.js к проекту:

1. Установите Vue CLI:
   ```
   npm install -g @vue/cli
   ```

2. Создайте новый Vue.js проект:
   ```
   vue create game-recommender-frontend
   ```

3. Настройте прокси в файле `vue.config.js`:
   ```javascript
   module.exports = {
     devServer: {
       proxy: {
         '/api': {
           target: 'http://localhost:8080',
           changeOrigin: true,
           pathRewrite: {
             '^/api': ''
           }
         }
       }
     }
   }
   ```

4. Создайте API-клиент для взаимодействия с бэкендом.

5. Разработайте компоненты для поиска игр, отображения рекомендаций и фильтрации.

6. Запустите фронтенд:
   ```
   cd game-recommender-frontend
   npm run serve
   ```

## Документация API

Документация API доступна по адресу `http://127.0.0.1:8000/docs` после запуска приложения.

## Технологии

- **Backend**: Python, FastAPI, SQLModel, SQLAlchemy, Pandas, NumPy, scikit-learn, SciPy
- **Database**: SQLite с поддержкой миграций через Alembic
- **Authentication**: JWT токены с хешированием паролей
- **File Storage**: Хранение аватаров в базе данных (BLOB)
- **External APIs**: Интеграция с Steam API для получения изображений игр
- **Frontend**: HTML, CSS, JavaScript (с возможностью подключения Vue.js)
- **Server**: Uvicorn (ASGI)

## Структура базы данных

- **games** - Информация об играх (название, рейтинг, цена, платформы)
- **game_metadata** - Метаданные игр (описание, теги)
- **users** - Пользователи системы (с аватарами)
- **user_ratings** - Оценки игр пользователями
- **game_lists** - Списки игр пользователей (играю, планирую, завершил)
- **recommendations** - Рекомендации на основе отзывов
- **balanced_recommendations** - Сбалансированные рекомендации для ML модели
