# Структура проекта Game Recommendation System

## Итоговая организация файлов

Проект был реорганизован для лучшего ориентирования и поддержки. Все файлы распределены по логическим папкам.

## Основные директории

### 📁 src/ - Основной исходный код
```
src/
├── api/                    # REST API эндпоинты
│   ├── __init__.py
│   ├── endpoints.py        # Основные эндпоинты (игры, рекомендации)
│   ├── auth_endpoints.py   # Аутентификация и пользователи
│   └── steamapi.py         # Интеграция с Steam API
├── models/                 # Модели данных
│   └── models.py           # SQLModel модели
├── cache/                  # Кэшированные данные
│   ├── *.pkl              # Сериализованные данные
│   └── cache_info.json    # Информация о кэше
├── auth.py                 # Система аутентификации
├── database.py             # Настройки БД
├── game_recommender_db.py  # Логика рекомендаций
├── game_lists.py           # Списки игр пользователей
├── user_ratings.py         # Оценки пользователей
├── main.py                 # FastAPI приложение
├── migrate_db.py           # Миграции БД
└── README.md               # Документация кода
```

### 📁 static/ - Веб-интерфейс
```
static/
├── index.html              # Главная страница
├── filter.html             # Страница фильтрации
├── api-client.js           # JavaScript API клиент
└── README.md               # Документация интерфейса
```

### 📁 scripts/ - Утилиты и скрипты
```
scripts/
├── check_*.py              # Скрипты проверки БД
├── migrate_*.py            # Скрипты миграции
├── show_*.py               # Скрипты просмотра данных
├── cache_to_db.py          # Перенос кэша в БД
└── README.md               # Документация скриптов
```

### 📁 tests/ - Комплексная система тестирования
```
tests/
├── test_comprehensive.py   # Функциональные тесты всех возможностей
├── test_performance.py     # Тесты производительности и нагрузки
├── run_tests.py            # Главный скрипт запуска тестов
├── test_app.py             # Базовые тесты приложения
├── test_auth.py            # Тесты аутентификации
├── simple_test.py          # Простые тесты
├── __init__.py             # Инициализация пакета
└── README.md               # Документация тестирования
```

### 📁 data/ - Данные
```
data/
└── games.db                # База данных игр
```

### 📁 migrations/ - Миграции Alembic
```
migrations/
├── versions/               # Файлы миграций
├── env.py                  # Настройки Alembic
└── script.py.mako          # Шаблон миграций
```

### 📁 archive/ - Архивные файлы
```
archive/
├── curs/                   # Учебные материалы
└── README.md               # Описание архива
```

## Корневые файлы

- `run.py` - Точка входа для запуска приложения
- `requirements.txt` - Зависимости Python
- `alembic.ini` - Настройки миграций
- `README.md` - Основная документация
- `.gitignore` - Исключения для Git

## Что было изменено

### ✅ Перемещено в scripts/
- Утилитарные скрипты (check_*, migrate_*, show_*, etc.)
- Скрипты для работы с БД и администрирования

### ✅ Создана папка tests/
- Комплексная система тестирования
- Функциональные тесты всех возможностей
- Тесты производительности и нагрузки
- Главный скрипт запуска тестов

### ✅ Перемещено в archive/
- Папка curs/ с учебными материалами
- Экспериментальные файлы

### ✅ Удалено
- Папки __pycache__ (добавлены в .gitignore)
- Дублирующиеся файлы

### ✅ Добавлено
- README.md файлы в каждой папке
- .gitignore для исключения временных файлов
- PROJECT_STRUCTURE.md (этот файл)

## Преимущества новой структуры

1. **Логическая организация** - файлы сгруппированы по назначению
2. **Легкая навигация** - понятно где искать нужный код
3. **Документированность** - каждая папка имеет описание
4. **Чистота** - архивные и временные файлы вынесены отдельно
5. **Масштабируемость** - легко добавлять новые модули

## Рекомендации по разработке

- Новые API эндпоинты добавляйте в `src/api/`
- Новые модели данных - в `src/models/models.py`
- Утилитарные скрипты - в `scripts/`
- Статические файлы - в `static/`
- Документацию обновляйте в соответствующих README.md
