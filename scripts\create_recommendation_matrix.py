import os
import pickle
import json
import time
from datetime import datetime
import pandas as pd
from sklearn.neighbors import NearestNeighbors
from scipy.sparse import coo_matrix
from sqlmodel import Session, select, func
from src.database import engine, create_db_and_tables
from src.models.models import Game, GameMetadata, Recommendation

# Пути для кеширования
CACHE_DIR = "src/cache"
GAMES_CACHE = os.path.join(CACHE_DIR, "games_cache.pkl")
METADATA_CACHE = os.path.join(CACHE_DIR, "metadata_cache.pkl")
BALANCED_RECOMMENDATIONS_CACHE = os.path.join(CACHE_DIR, "balanced_recommendations_cache.pkl")
RECOMMENDATION_MATRIX_CACHE = os.path.join(CACHE_DIR, "recommendation_matrix_cache.pkl")
CACHE_INFO = os.path.join(CACHE_DIR, "cache_info.json")

def create_cache_dir():
    """Создает директорию для кэша, если она не существует"""
    if not os.path.exists(CACHE_DIR):
        os.makedirs(CACHE_DIR)
        print(f"Создана директория для кэша: {CACHE_DIR}")

def load_data_from_database(max_recommendations=5000000):
    """
    Загружает данные из базы данных

    Args:
        max_recommendations: Максимальное количество рекомендаций для загрузки
    """
    # Создаем базу данных и таблицы, если они не существуют
    create_db_and_tables()

    with Session(engine) as session:
        # Загружаем игры
        games = pd.DataFrame([game.model_dump() for game in session.exec(select(Game))])
        print(f"Загружено {len(games)} игр из базы данных")

        # Загружаем метаданные
        metadata_records = session.exec(select(GameMetadata)).all()
        metadata = pd.DataFrame([
            {
                "app_id": record.app_id,
                "description": record.description,
                "tags": json.loads(record.tags_json) if record.tags_json else []
            }
            for record in metadata_records
        ])
        print(f"Загружено {len(metadata)} метаданных из базы данных")

        # Получаем общее количество рекомендаций
        total_recommendations = session.exec(select(func.count()).select_from(Recommendation)).one()
        print(f"Всего рекомендаций в базе данных: {total_recommendations}")

        # Загружаем рекомендации с ограничением
        print(f"Загрузка до {max_recommendations} рекомендаций из базы данных...")
        recommendations_query = select(Recommendation).limit(max_recommendations)
        recommendations = pd.DataFrame([rec.model_dump() for rec in session.exec(recommendations_query)])
        print(f"Загружено {len(recommendations)} рекомендаций из базы данных")

    return games, metadata, recommendations

def prepare_recommendation_matrix(games, recommendations):
    """Подготавливает матрицу пользователь-игра для рекомендаций"""
    try:
        # Объединяем данные рекомендаций с данными игр
        merged = pd.merge(
            recommendations,
            games[['app_id', 'title']],
            on='app_id',
            how='inner'
        )

        print(f"Исходное количество записей: {len(merged)}")

        # Ограничиваем количество пользователей и игр для экономии памяти
        user_counts = merged['user_id'].value_counts()
        game_counts = merged['app_id'].value_counts()

        # Устанавливаем минимальные пороги для фильтрации
        min_user_ratings = 1  # Минимальное количество рейтингов от пользователя
        min_game_ratings = 1  # Минимальное количество рейтингов для игры

        active_users = user_counts[user_counts >= min_user_ratings].index
        popular_games = game_counts[game_counts >= min_game_ratings].index

        filtered_merged = merged[
            merged['user_id'].isin(active_users) &
            merged['app_id'].isin(popular_games)
        ]

        print(f"Отфильтрованное количество записей: {len(filtered_merged)}")
        print(f"Количество пользователей: {len(active_users)}, количество игр: {len(popular_games)}")

        # Проверяем, есть ли достаточно данных для создания матрицы
        if len(filtered_merged) > 0 and len(active_users) > 0 and len(popular_games) > 0:
            # Создаем словари для маппинга ID в индексы
            game_to_idx = {game_id: i for i, game_id in enumerate(popular_games)}
            idx_to_game = {i: game_id for game_id, i in game_to_idx.items()}

            # Создаем словарь для быстрого поиска индексов пользователей
            user_to_idx = {user_id: i for i, user_id in enumerate(active_users)}

            # Получаем индексы строк, столбцов и значения
            row_indices = []
            col_indices = []

            # Используем более эффективный способ создания индексов
            for _, row in filtered_merged.iterrows():
                app_id = row['app_id']
                user_id = row['user_id']
                if app_id in game_to_idx and user_id in user_to_idx:
                    row_indices.append(game_to_idx[app_id])
                    col_indices.append(user_to_idx[user_id])

            data = [1] * len(row_indices)  # Все значения равны 1 (is_recommended=True)

            # Создаем разреженную матрицу
            user_game_matrix_sparse = coo_matrix(
                (data, (row_indices, col_indices)),
                shape=(len(popular_games), len(active_users))
            ).tocsr()

            # Создаем модель ближайших соседей
            knn = NearestNeighbors(metric='cosine', algorithm='brute')
            knn.fit(user_game_matrix_sparse)
            has_recommendation_model = True

            # Формируем данные для сохранения
            recommendation_data = {
                'user_game_matrix_sparse': user_game_matrix_sparse,
                'knn': knn,
                'game_to_idx': game_to_idx,
                'idx_to_game': idx_to_game,
                'popular_games': popular_games,
                'has_recommendation_model': has_recommendation_model
            }

            print("Модель рекомендаций успешно создана")
            return recommendation_data
        else:
            print("Недостаточно данных для создания модели рекомендаций")
            return None
    except Exception as e:
        print(f"Ошибка при создании матрицы рекомендаций: {str(e)}")
        return None

def save_to_cache(games, metadata, recommendations, recommendation_data):
    """Сохраняет данные в кэш"""
    try:
        # Создаем директорию для кэша, если она не существует
        create_cache_dir()

        # Сохраняем игры
        with open(GAMES_CACHE, 'wb') as f:
            pickle.dump(games, f)
        print(f"Сохранено {len(games)} игр в кэш")

        # Сохраняем метаданные
        with open(METADATA_CACHE, 'wb') as f:
            pickle.dump(metadata, f)
        print(f"Сохранено {len(metadata)} метаданных в кэш")

        # Сохраняем рекомендации
        with open(BALANCED_RECOMMENDATIONS_CACHE, 'wb') as f:
            pickle.dump(recommendations, f)
        print(f"Сохранено {len(recommendations)} рекомендаций в кэш")

        # Сохраняем матрицу рекомендаций
        if recommendation_data:
            with open(RECOMMENDATION_MATRIX_CACHE, 'wb') as f:
                pickle.dump(recommendation_data, f)
            print(f"Сохранена матрица рекомендаций с {len(recommendation_data['popular_games'])} играми")

            # Сохраняем информацию о кэше
            cache_info = {
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'games_count': len(games),
                'metadata_count': len(metadata),
                'recommendations_count': len(recommendations),
                'popular_games_count': len(recommendation_data['popular_games'])
            }
            with open(CACHE_INFO, 'w') as f:
                json.dump(cache_info, f, indent=4)
            print(f"Сохранена информация о кэше")

        return True
    except Exception as e:
        print(f"Ошибка при сохранении данных в кэш: {str(e)}")
        return False

# Функция сохранения в базу данных больше не используется
# def save_balanced_recommendations_to_db(recommendations_df):
#     """Сохраняет сбалансированные рекомендации в базу данных"""
#     # Эта функция больше не используется, так как мы используем только кэшированные рекомендации
#     pass

def main():
    """Основная функция для создания матрицы рекомендаций и сохранения её только в кэше"""
    start_time = time.time()

    print("Начало создания матрицы рекомендаций...")

    # Загружаем данные из базы данных
    games, metadata, recommendations = load_data_from_database()

    # Подготавливаем матрицу рекомендаций
    recommendation_data = prepare_recommendation_matrix(games, recommendations)

    # Сохраняем данные в кэш
    cache_success = save_to_cache(games, metadata, recommendations, recommendation_data)

    end_time = time.time()
    print(f"Создание матрицы рекомендаций завершено за {end_time - start_time:.2f} секунд")

    if cache_success:
        print("Матрица рекомендаций успешно создана и сохранена в кэше")
        print(f"Теперь вы можете запустить приложение с помощью команды: python main.py")
    else:
        print("Не удалось создать матрицу рекомендаций")

if __name__ == "__main__":
    main()
