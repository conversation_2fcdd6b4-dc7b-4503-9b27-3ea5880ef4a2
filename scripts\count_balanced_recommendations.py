from sqlmodel import Session, select, func
from src.database import engine
from src.models.models import BalancedRecommendation

def count_balanced_recommendations():
    with Session(engine) as session:
        count = len(list(session.exec(select(BalancedRecommendation))))
        print(f'Количество записей в таблице balanced_recommendations: {count}')

if __name__ == "__main__":
    count_balanced_recommendations()


