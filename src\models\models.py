from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime, timezone
from sqlalchemy import JSON, Column, LargeBinary
from pydantic import BaseModel
from enum import Enum

class GameBase(SQLModel):
    """Базовая модель для игры"""
    title: str = Field(index=True)
    date_release: Optional[datetime] = Field(default=None)
    win: bool = Field(default=False)
    mac: bool = Field(default=False)
    linux: bool = Field(default=False)
    rating: Optional[str] = Field(default=None)
    positive_ratio: Optional[int] = Field(default=None)
    user_reviews: Optional[int] = Field(default=None)
    price_final: Optional[float] = Field(default=None)
    price_original: Optional[float] = Field(default=None)
    discount: Optional[float] = Field(default=None)
    steam_deck: Optional[bool] = Field(default=False)

class Game(GameBase, table=True):
    """Модель игры для базы данных"""
    __tablename__ = "games"

    app_id: int = Field(primary_key=True)

    # Отношения
    game_metadata: Optional["GameMetadata"] = Relationship(back_populates="game")
    recommendations: List["Recommendation"] = Relationship(back_populates="game")
    balanced_recommendations: List["BalancedRecommendation"] = Relationship(back_populates="game")
    user_ratings: List["UserRating"] = Relationship(back_populates="game")
    game_lists: List["GameList"] = Relationship(back_populates="game")

class GameRead(GameBase):
    """Модель для чтения данных об игре"""
    app_id: int

class GameCreate(GameBase):
    """Модель для создания игры"""
    app_id: int

class GameMetadataBase(SQLModel):
    """Базовая модель для метаданных игры"""
    description: Optional[str] = Field(default=None)
    tags_json: Optional[str] = Field(default=None, sa_column=Field(JSON))

class GameMetadata(GameMetadataBase, table=True):
    """Модель метаданных игры для базы данных"""
    __tablename__ = "game_metadata"

    id: Optional[int] = Field(default=None, primary_key=True)
    app_id: int = Field(foreign_key="games.app_id")

    # Отношения
    game: Game = Relationship(back_populates="game_metadata")

class GameMetadataRead(GameMetadataBase):
    """Модель для чтения метаданных игры"""
    id: int
    app_id: int

class GameMetadataCreate(GameMetadataBase):
    """Модель для создания метаданных игры"""
    app_id: int

class RecommendationBase(SQLModel):
    """Базовая модель для рекомендации"""
    helpful: Optional[int] = Field(default=None)
    funny: Optional[int] = Field(default=None)
    date: Optional[datetime] = Field(default=None)
    is_recommended: bool = Field(default=True)
    hours: Optional[float] = Field(default=None)

class Recommendation(RecommendationBase, table=True):
    """Модель рекомендации для базы данных"""
    __tablename__ = "recommendations"

    id: Optional[int] = Field(default=None, primary_key=True)
    app_id: int = Field(foreign_key="games.app_id")
    user_id: int = Field(index=True)
    review_id: Optional[int] = Field(default=None)

    # Отношения
    game: Game = Relationship(back_populates="recommendations")

class RecommendationRead(RecommendationBase):
    """Модель для чтения рекомендации"""
    id: int
    app_id: int
    user_id: int
    review_id: Optional[int] = None

class RecommendationCreate(RecommendationBase):
    """Модель для создания рекомендации"""
    app_id: int
    user_id: int
    review_id: Optional[int] = None

class BalancedRecommendation(RecommendationBase, table=True):
    """Модель сбалансированной рекомендации для базы данных"""
    __tablename__ = "balanced_recommendations"

    id: Optional[int] = Field(default=None, primary_key=True)
    app_id: int = Field(foreign_key="games.app_id")
    user_id: int = Field(index=True)
    review_id: Optional[int] = Field(default=None)

    # Отношения
    game: Game = Relationship(back_populates="balanced_recommendations")

class UserBase(SQLModel):
    """Базовая модель пользователя"""
    username: str = Field(index=True, unique=True)
    email: str = Field(unique=True, index=True)
    is_active: bool = Field(default=True)

class User(UserBase, table=True):
    """Модель пользователя для базы данных"""
    __tablename__ = "users"

    id: Optional[int] = Field(default=None, primary_key=True)
    hashed_password: str = Field(default="")
    avatar: Optional[bytes] = Field(default=None, sa_column=Column(LargeBinary))
    avatar_content_type: Optional[str] = Field(default=None)

    # Отношения
    ratings: List["UserRating"] = Relationship(back_populates="user")
    game_lists: List["GameList"] = Relationship(back_populates="user")

class UserCreate(BaseModel):
    """Модель для создания пользователя"""
    username: str
    email: str
    password: str

class UserRead(UserBase):
    """Модель для чтения данных пользователя"""
    id: int
    has_avatar: bool = False
    avatar_content_type: Optional[str] = None

class UserRating(SQLModel, table=True):
    """Модель оценки игры пользователем"""
    __tablename__ = "user_ratings"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    app_id: int = Field(foreign_key="games.app_id", index=True)
    rating: int = Field(ge=1, le=5)  # Оценка от 1 до 5
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Отношения
    user: User = Relationship(back_populates="ratings")
    game: Game = Relationship(back_populates="user_ratings")

    # Уникальное ограничение: один пользователь может оставить только одну оценку для одной игры
    class Config:
        table_constraints = [
            {"name": "uq_user_game_rating", "constraint": "UNIQUE(user_id, app_id)"}
        ]

class GameListType(str, Enum):
    """Типы списков игр"""
    PLAYING = "playing"
    PLANNED = "planned"
    COMPLETED = "completed"

class GameList(SQLModel, table=True):
    """Модель списка игр пользователя"""
    __tablename__ = "game_lists"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    app_id: int = Field(foreign_key="games.app_id", index=True)
    list_type: GameListType = Field(index=True)
    added_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Отношения
    user: User = Relationship(back_populates="game_lists")
    game: Game = Relationship(back_populates="game_lists")

    # Уникальное ограничение: одна игра может быть только в одном списке у пользователя
    class Config:
        table_constraints = [
            {"name": "uq_user_game_list", "constraint": "UNIQUE(user_id, app_id)"}
        ]