"""
Миграция таблицы пользователей для совместимости с SQLModel
"""
import os
import sqlite3
import sys

# Добавляем текущую директорию в путь Python
sys.path.append(os.path.abspath('.'))

def main():
    """Основная функция миграции"""
    # Пути к базам данных
    db_paths = [
        "src/database.db",
        "data/games.db"
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"Миграция таблицы users в базе данных {db_path}...")
            migrate_users_table(db_path)
        else:
            print(f"База данных не найдена: {db_path}")

def migrate_users_table(db_path):
    """Миграция таблицы users"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем, существует ли таблица users
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print(f"Таблица users не найдена в базе данных {db_path}")
            conn.close()
            return
        
        # Получаем текущую структуру таблицы users
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"Текущая структура таблицы users:")
        for col in columns:
            print(f"- {col[1]} ({col[2]})")
        
        # Создаем временную таблицу с новой структурой
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            hashed_password TEXT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 1
        )
        """)
        
        # Копируем данные из старой таблицы в новую
        if "hashed_password" in column_names:
            # Если колонка hashed_password уже существует
            cursor.execute("""
            INSERT INTO users_new (id, username, email, hashed_password, is_active)
            SELECT id, username, email, hashed_password, 1 FROM users
            """)
        else:
            # Если колонки hashed_password нет, используем пустую строку
            cursor.execute("""
            INSERT INTO users_new (id, username, email, hashed_password, is_active)
            SELECT id, username, email, '', 1 FROM users
            """)
        
        # Удаляем старую таблицу
        cursor.execute("DROP TABLE users")
        
        # Переименовываем новую таблицу
        cursor.execute("ALTER TABLE users_new RENAME TO users")
        
        # Получаем обновленную структуру таблицы users
        cursor.execute("PRAGMA table_info(users)")
        updated_columns = cursor.fetchall()
        
        print(f"Обновленная структура таблицы users:")
        for col in updated_columns:
            print(f"- {col[1]} ({col[2]})")
        
        conn.commit()
        conn.close()
        print(f"Таблица users в базе данных {db_path} успешно обновлена")
    except Exception as e:
        print(f"Ошибка при миграции таблицы users: {str(e)}")

if __name__ == "__main__":
    main()
