"""
Демонстрация работы функционала поиска ID игры по названию
Этот скрипт тестирует логику без запуска веб-сервера
"""
import os
import sys

# Добавляем текущую директорию в путь Python
sys.path.append(os.path.abspath('.'))

from sqlmodel import Session, select
from src.database import create_db_and_tables, engine
from src.models.models import Game
from src.game_recommender_db import GameRecommender

def test_game_search_logic(recommender):
    """Тестирование логики поиска игр"""
    print("Демонстрация работы поиска ID игры по названию...")
    
    # Получаем несколько игр из базы данных для демонстрации
    print("\n1. Получение игр из базы данных...")
    with Session(engine) as session:
        games = session.exec(select(Game).limit(5)).all()

        if not games:
            print("❌ В базе данных нет игр для демонстрации")
            return

        print(f"✓ Найдено {len(games)} игр:")
        for i, game in enumerate(games, 1):
            print(f"  {i}. {game.title} (ID: {game.app_id})")
    
    # Демонстрация различных сценариев поиска
    test_cases = [
        {
            "title": games[0].title,
            "description": "Поиск по точному названию"
        },
        {
            "title": games[0].title[:10],
            "description": "Поиск по части названия"
        },
        {
            "title": "Counter",
            "description": "Поиск популярной игры"
        },
        {
            "title": "НесуществующаяИгра",
            "description": "Поиск несуществующей игры"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 2):
        print(f"\n{i}. {test_case['description']}: '{test_case['title']}'")

        # Тестируем новую логику эндпоинта - только точное совпадение
        games_df = recommender.games
        exact_match = games_df[games_df['title'] == test_case['title']]

        if exact_match.empty:
            print("❌ Игра с точным названием не найдена")
        else:
            game = exact_match.iloc[0]
            print("✓ Найдено точное совпадение:")
            print(f"  - ID игры: {int(game['app_id'])}")
            print(f"  - Название: {game['title']}")
            print(f"  - Рейтинг: {game.get('rating', 'Не указан')}")

def simulate_endpoint_response(title: str, recommender):
    """Симуляция ответа нашего эндпоинта (только точное совпадение)"""
    print(f"\n--- Симуляция эндпоинта для '{title}' ---")

    try:
        # Новая логика эндпоинта - только точное совпадение
        games_df = recommender.games
        exact_match = games_df[games_df['title'] == title]

        if exact_match.empty:
            print("❌ Ответ эндпоинта: 404 - Игра с точным названием не найдена")
            return

        # Берем первую найденную игру
        game = exact_match.iloc[0]

        response = {
            "query": title,
            "found": True,
            "game_id": int(game["app_id"]),
            "game_title": game["title"]
        }

        print("✓ Ответ эндпоинта (точное совпадение):")

        # Выводим JSON-подобный ответ
        import json
        print(json.dumps(response, ensure_ascii=False, indent=2))

    except Exception as e:
        print(f"❌ Ошибка: {str(e)}")

def main():
    """Основная функция демонстрации"""
    print("Демонстрация функционала поиска ID игры по названию")
    print("=" * 60)

    # Создаем таблицы (если нужно)
    create_db_and_tables()

    # Создаем ОДИН экземпляр рекомендательной системы
    print("\n1. Инициализация рекомендательной системы...")
    recommender = GameRecommender()
    print("✓ Рекомендательная система инициализирована")

    # Тестируем логику поиска
    test_game_search_logic(recommender)

    print("\n" + "=" * 60)
    print("Симуляция ответов эндпоинта (только точные названия):")

    # Получаем несколько точных названий игр из базы данных для тестирования
    with Session(engine) as session:
        games = session.exec(select(Game).limit(3)).all()  # Уменьшаем до 3 игр

        if games:
            print(f"\nТестируем эндпоинт с точными названиями из базы данных:")
            for game in games:
                simulate_endpoint_response(game.title, recommender)

    # Также тестируем с неточными названиями (должны вернуть 404)
    print(f"\nТестируем эндпоинт с неточными названиями (ожидаем 404):")
    test_queries = ["Counter", "Dota", "Half", "НесуществующаяИгра"]  # Уменьшаем количество тестов

    for query in test_queries:
        simulate_endpoint_response(query, recommender)
    
    print("\n" + "=" * 60)
    print("Демонстрация завершена!")
    print("\nДля тестирования реального эндпоинта:")
    print("1. Запустите сервер: uvicorn main:app --reload")
    print("2. Запустите тест: python tests/test_game_id_endpoint.py")
    print("3. Или откройте в браузере: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
