import httpx
from src.config import settings


async def get_steam_game_image(appid: int) -> str:
    """
    Получает URL изображения игры из Steam API

    Args:
        appid: ID игры в Steam

    Returns:
        URL изображения игры

    Raises:
        Exception: Если не удалось получить изображение
    """
    url = f"https://store.steampowered.com/api/appdetails?appids={appid}"
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=settings.STEAM_API_TIMEOUT)

        if response.status_code != 200:
            print(f"Steam API вернул статус {response.status_code} для игры {appid}")
            raise Exception(f"Failed to contact Steam API. Status code: {response.status_code}")

        data_json = response.json()
        app_data = data_json.get(str(appid), {})

        if not app_data.get("success", False):
            print(f"Steam API не нашел игру с ID {appid}")
            raise Exception(f"Game with ID {appid} not found in Steam API.")

        if "data" not in app_data:
            print(f"Steam API вернул ответ без данных для игры {appid}")
            raise Exception(f"No data returned from Steam API for game {appid}")

        image_url = app_data["data"].get("header_image")
        if not image_url:
            print(f"Steam API не вернул изображение для игры {appid}")
            raise Exception(f"Image not found for game {appid}")

        return image_url
    except httpx.TimeoutException:
        print(f"Таймаут при запросе к Steam API для игры {appid}")
        raise Exception(f"Timeout when contacting Steam API for game {appid}")
    except httpx.RequestError as e:
        print(f"Ошибка запроса к Steam API для игры {appid}: {str(e)}")
        raise Exception(f"Request error when contacting Steam API: {str(e)}")
    except Exception as e:
        print(f"Неизвестная ошибка при получении изображения для игры {appid}: {str(e)}")
        raise Exception(f"Error getting image for game {appid}: {str(e)}")