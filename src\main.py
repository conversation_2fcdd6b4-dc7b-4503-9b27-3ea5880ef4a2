from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from src.api import main_router
from src.database import create_db_and_tables
from src.migrate_db import main as migrate_db
from contextlib import asynccontextmanager
import os

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Создаем базу данных и таблицы при запуске приложения
    create_db_and_tables()
    print("База данных и таблицы успешно созданы")

    # Мигрируем базу данных для совместимости с SQLModel
    migrate_db()
    print("Миграция базы данных успешно выполнена")

    yield
    # Здесь можно добавить код для выполнения при завершении приложения

app = FastAPI(
    title="Система рекомендаций игр",
    description="Асинхронный API для рекомендации игр с использованием гибридного метода рекомендаций",
    lifespan=lifespan,
    swagger_ui_parameters={"defaultModelsExpandDepth": -1}  # Скрываем модели по умолчанию
)

# Настройка CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Разрешаем запросы с любых доменов (в продакшене лучше указать конкретные домены)
    allow_credentials=True,
    allow_methods=["*"],  # Разрешаем все HTTP методы
    allow_headers=["*"],  # Разрешаем все заголовки
)

# Монтируем статические файлы
static_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "static")
if not os.path.exists(static_dir):
    os.makedirs(static_dir)
app.mount("/static", StaticFiles(directory=static_dir), name="static")

app.include_router(main_router)


if __name__ == '__main__':
    import uvicorn
    port = 8000
    print(f"Запуск веб-сервера на http://127.0.0.1:{port}")
    print(f"Документация доступна по адресу http://127.0.0.1:{port}/docs")
    print("Система рекомендаций игр Steam")
    print("Используется гибридный метод рекомендаций, который комбинирует:")
    print("  - Коллаборативную фильтрацию (на основе похожести игр по оценкам пользователей)")
    print("  - Контент-бейсд фильтрацию (на основе похожести тегов игр)")
    print("  - Рейтинг и популярность игр")
    uvicorn.run(app, host="127.0.0.1", port=port)