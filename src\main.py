from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.api import main_router
from src.database import create_db_and_tables
from src.migrate_db import main as migrate_db
from src.config import settings
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Создаем базу данных и таблицы при запуске приложения
    create_db_and_tables()
    print("База данных и таблицы успешно созданы")

    # Мигрируем базу данных для совместимости с SQLModel
    migrate_db()
    print("Миграция базы данных успешно выполнена")

    yield
    # Здесь можно добавить код для выполнения при завершении приложения

app = FastAPI(
    title="Система рекомендаций игр",
    description="Асинхронный API для рекомендации игр с использованием гибридного метода рекомендаций",
    lifespan=lifespan,
    debug=settings.DEBUG,
    swagger_ui_parameters={"defaultModelsExpandDepth": -1}  # Скрываем модели по умолчанию
)

# Настройка CORS из переменных окружения
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=settings.CORS_CREDENTIALS,
    allow_methods=settings.cors_methods_list,
    allow_headers=settings.cors_headers_list,
)



app.include_router(main_router)


if __name__ == '__main__':
    import uvicorn
    print(f"Запуск веб-сервера на http://{settings.HOST}:{settings.PORT}")
    print(f"Документация доступна по адресу http://{settings.HOST}:{settings.PORT}/docs")
    print(f"Режим работы: {settings.ENVIRONMENT}")
    print("Система рекомендаций игр Steam")
    print("Используется гибридный метод рекомендаций, который комбинирует:")
    print("  - Коллаборативную фильтрацию (на основе похожести игр по оценкам пользователей)")
    print("  - Контент-бейсд фильтрацию (на основе похожести тегов игр)")
    print("  - Рейтинг и популярность игр")

    # Используем строку импорта вместо объекта приложения для поддержки reload
    uvicorn.run(
        "src.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )