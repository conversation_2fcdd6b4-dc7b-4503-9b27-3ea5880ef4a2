#!/usr/bin/env python3
"""
Главный скрипт для запуска всех тестов системы рекомендаций игр Steam
"""

import sys
import os
import subprocess
import time
import argparse

def print_header(title: str):
    """Печать заголовка"""
    print("\n" + "=" * 60)
    print(f"🎮 {title}")
    print("=" * 60)

def run_comprehensive_tests():
    """Запуск комплексных тестов"""
    print_header("КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ")
    print("Запуск полного набора функциональных тестов...")
    print()
    
    try:
        result = subprocess.run([
            sys.executable, "tests/test_comprehensive.py"
        ], capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Ошибка при запуске комплексных тестов: {str(e)}")
        return False

def run_performance_tests():
    """Запуск тестов производительности"""
    print_header("ТЕСТИРОВАНИЕ ПРОИЗВОДИТЕЛЬНОСТИ")
    print("Запуск тестов производительности и нагрузки...")
    print()
    
    try:
        result = subprocess.run([
            sys.executable, "tests/test_performance.py"
        ], capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Ошибка при запуске тестов производительности: {str(e)}")
        return False

def check_app_running():
    """Проверка, что приложение запущено"""
    print("🔍 Проверка доступности приложения...")
    
    try:
        import httpx
        response = httpx.get("http://127.0.0.1:8000/", timeout=5)
        if response.status_code in [200, 307]:
            print("✅ Приложение доступно")
            return True
        else:
            print(f"⚠️  Приложение отвечает с кодом {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Приложение недоступно: {str(e)}")
        print("💡 Убедитесь, что приложение запущено: python run.py")
        return False

def main():
    """Основная функция"""
    parser = argparse.ArgumentParser(
        description="Запуск тестов для системы рекомендаций игр Steam",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  python tests/run_tests.py                    # Все тесты
  python tests/run_tests.py --comprehensive    # Только функциональные тесты
  python tests/run_tests.py --performance      # Только тесты производительности
  python tests/run_tests.py --quick            # Быстрая проверка
        """
    )
    
    parser.add_argument(
        "--comprehensive", 
        action="store_true", 
        help="Запустить только комплексные функциональные тесты"
    )
    parser.add_argument(
        "--performance", 
        action="store_true", 
        help="Запустить только тесты производительности"
    )
    parser.add_argument(
        "--quick", 
        action="store_true", 
        help="Быстрая проверка основных функций"
    )
    parser.add_argument(
        "--skip-check", 
        action="store_true", 
        help="Пропустить проверку доступности приложения"
    )
    
    args = parser.parse_args()
    
    print("🎮 Game Recommendation System - Test Runner")
    print("Система запуска тестов")
    
    # Проверка доступности приложения
    if not args.skip_check:
        if not check_app_running():
            print("\n❌ КРИТИЧЕСКАЯ ОШИБКА: Приложение недоступно!")
            print("Запустите приложение перед тестированием: python run.py")
            return 1
    
    start_time = time.time()
    results = []
    
    # Определение какие тесты запускать
    run_comprehensive = args.comprehensive or (not args.performance and not args.quick)
    run_performance = args.performance or (not args.comprehensive and not args.quick)
    
    if args.quick:
        run_comprehensive = True
        run_performance = False
        print("\n🚀 БЫСТРЫЙ РЕЖИМ: Запуск только функциональных тестов")
    
    # Запуск тестов
    if run_comprehensive:
        success = run_comprehensive_tests()
        results.append(("Комплексные тесты", success))
    
    if run_performance:
        success = run_performance_tests()
        results.append(("Тесты производительности", success))
    
    # Итоговый отчет
    end_time = time.time()
    total_time = end_time - start_time
    
    print_header("ИТОГОВЫЙ ОТЧЕТ")
    print(f"⏱️  Общее время выполнения: {total_time:.2f} секунд")
    print()
    
    all_passed = True
    for test_name, success in results:
        status = "✅ ПРОЙДЕН" if success else "❌ ПРОВАЛЕН"
        print(f"{status} {test_name}")
        if not success:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        print("Система работает корректно.")
        return_code = 0
    else:
        print("💥 НЕКОТОРЫЕ ТЕСТЫ ПРОВАЛИЛИСЬ!")
        print("Проверьте детали выше и исправьте проблемы.")
        return_code = 1
    
    print("\n📚 Дополнительная информация:")
    print("  • Детальные отчеты сохранены в папке tests/")
    print("  • Для помощи: python tests/run_tests.py --help")
    print("  • Документация API: http://127.0.0.1:8000/docs")
    
    return return_code

if __name__ == "__main__":
    sys.exit(main())
