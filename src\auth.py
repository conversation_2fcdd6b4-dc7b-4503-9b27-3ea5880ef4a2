from datetime import datetime, timedelta, timezone
from typing import Optional
import hashlib
import base64
import hmac
import json
import os

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlmodel import Session, select
from pydantic import BaseModel

from src.database import get_session
from src.models.models import User as UserModel, User<PERSON><PERSON> as User<PERSON>reateMode<PERSON>, UserRead

# Настройки безопасности
SECRET_KEY = "your-secret-key-for-jwt-token-generation"  # В реальном приложении используйте безопасный ключ
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Модели данных для аутентификации
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserAuth(BaseModel):
    """Модель пользователя для аутентификации"""
    username: str
    email: Optional[str] = None
    id: Optional[int] = None
    is_active: bool = True
    has_avatar: bool = False
    avatar_content_type: Optional[str] = None

class UserInDB(UserAuth):
    """Модель пользователя с хешем пароля"""
    hashed_password: str

# Инструменты для работы с паролями и токенами
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Проверяет соответствие пароля хешу"""
    if not hashed_password or ':' not in hashed_password:
        return False

    salt, stored_hash = hashed_password.split(':')
    calculated_hash = hashlib.sha256((plain_password + salt).encode()).hexdigest()
    return hmac.compare_digest(calculated_hash, stored_hash)

def get_password_hash(password: str) -> str:
    """Создает хеш пароля с солью"""
    salt = base64.b64encode(os.urandom(16)).decode()
    password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
    return f"{salt}:{password_hash}"

def get_user_by_username(username: str, session: Session) -> Optional[UserInDB]:
    """Получает пользователя из базы данных по имени пользователя"""
    user = session.exec(select(UserModel).where(UserModel.username == username)).first()

    if user:
        return UserInDB(
            username=user.username,
            email=user.email,
            hashed_password=user.hashed_password,
            id=user.id,
            is_active=user.is_active,
            has_avatar=user.avatar is not None,
            avatar_content_type=user.avatar_content_type
        )
    return None

def create_user(user_data: UserCreateModel, session: Session) -> UserRead:
    """Создает нового пользователя в базе данных"""
    # Проверяем, существует ли пользователь с таким именем или email
    existing_user = session.exec(
        select(UserModel).where(
            (UserModel.username == user_data.username) |
            (UserModel.email == user_data.email)
        )
    ).first()

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Имя пользователя или email уже зарегистрированы"
        )

    # Создаем нового пользователя
    hashed_password = get_password_hash(user_data.password)

    # Создаем объект пользователя
    db_user = UserModel(
        username=user_data.username,
        email=user_data.email,
        hashed_password=hashed_password,
        is_active=True
    )

    # Добавляем пользователя в базу данных
    session.add(db_user)
    session.commit()
    session.refresh(db_user)

    # Возвращаем данные пользователя без пароля
    return UserRead.model_validate(db_user)

def authenticate_user(username: str, password: str, session: Session) -> Optional[UserInDB]:
    """Аутентифицирует пользователя по имени и паролю"""
    user = get_user_by_username(username, session)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Создает токен доступа"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire.timestamp()})

    # Создаем токен как JSON строку, закодированную в base64
    token_bytes = json.dumps(to_encode).encode()
    encoded_token = base64.urlsafe_b64encode(token_bytes).decode()

    # Добавляем подпись для проверки целостности
    signature = hmac.new(
        SECRET_KEY.encode(),
        encoded_token.encode(),
        hashlib.sha256
    ).hexdigest()

    return f"{encoded_token}.{signature}"

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    session: Session = Depends(get_session)
) -> UserAuth:
    """Получает текущего пользователя по токену"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Не удалось проверить учетные данные. Пожалуйста, войдите в систему снова.",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # Разделяем токен на данные и подпись
        token_parts = token.split('.')
        if len(token_parts) != 2:
            raise credentials_exception

        encoded_token, signature = token_parts

        # Проверяем подпись
        expected_signature = hmac.new(
            SECRET_KEY.encode(),
            encoded_token.encode(),
            hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(signature, expected_signature):
            raise credentials_exception

        # Декодируем данные токена
        token_bytes = base64.urlsafe_b64decode(encoded_token)
        payload = json.loads(token_bytes.decode())

        # Проверяем срок действия токена
        exp = payload.get("exp")
        if exp is None or datetime.now(timezone.utc).timestamp() > exp:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Срок действия токена истек. Пожалуйста, войдите в систему снова.",
                headers={"WWW-Authenticate": "Bearer"},
            )

        username = payload.get("sub")
        if username is None:
            raise credentials_exception

        token_data = TokenData(username=username)
    except HTTPException:
        raise
    except Exception:
        raise credentials_exception

    user = get_user_by_username(username=token_data.username, session=session)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Пользователь не найден. Пожалуйста, зарегистрируйтесь.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Пользователь неактивен. Пожалуйста, обратитесь к администратору.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return UserAuth(
        username=user.username,
        email=user.email,
        id=user.id,
        is_active=user.is_active,
        has_avatar=user.has_avatar,
        avatar_content_type=user.avatar_content_type
    )
