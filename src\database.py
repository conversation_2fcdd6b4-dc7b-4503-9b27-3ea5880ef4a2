from sqlmodel import SQLModel, create_engine, Session
import os
from typing import Generator

# Получаем путь к текущей директории
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Создаем URL для подключения к базе данных SQLite
DATABASE_URL = f"sqlite:///{os.path.join(BASE_DIR, 'database.db')}"

# Создаем движок для работы с базой данных
engine = create_engine(DATABASE_URL, echo=True)

def create_db_and_tables():
    """Создает базу данных и таблицы"""
    SQLModel.metadata.create_all(engine)

def get_session() -> Generator[Session, None, None]:
    """Возвращает сессию для работы с базой данных"""
    with Session(engine) as session:
        yield session
