<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Фильтрация игр Steam</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .filter-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .filter-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }
        .tag {
            background: #e0e0e0;
            padding: 5px 10px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .tag-remove {
            margin-left: 5px;
            cursor: pointer;
            font-weight: bold;
            color: #666;
        }
        button {
            padding: 10px 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #45a049;
        }
        #results {
            margin-top: 20px;
        }
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        .game-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .game-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        .game-rating {
            color: #666;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .game-positive-ratio {
            font-size: 14px;
            color: #4CAF50;
        }
        .filter-summary {
            margin-bottom: 15px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
        .filter-badge {
            display: inline-block;
            background: #e0e0e0;
            padding: 3px 8px;
            border-radius: 10px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            margin: 0 10px;
            color: #4CAF50;
            text-decoration: none;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Фильтрация игр Steam</h1>
        
        <div class="nav-links">
            <a href="/static/index.html">Главная</a>
            <a href="/static/filter.html">Фильтрация игр</a>
        </div>
        
        <div class="filter-container">
            <div class="filter-group">
                <label for="year-min">Минимальный год выпуска:</label>
                <select id="year-min">
                    <option value="">Не выбрано</option>
                    <!-- Годы будут добавлены динамически -->
                </select>
            </div>
            
            <div class="filter-group">
                <label for="year-max">Максимальный год выпуска:</label>
                <select id="year-max">
                    <option value="">Не выбрано</option>
                    <!-- Годы будут добавлены динамически -->
                </select>
            </div>
            
            <div class="filter-group">
                <label for="tag-select">Теги:</label>
                <select id="tag-select">
                    <option value="">Выберите тег</option>
                    <!-- Теги будут добавлены динамически -->
                </select>
                <div class="tag-container" id="selected-tags">
                    <!-- Выбранные теги будут добавлены сюда -->
                </div>
            </div>
            
            <div class="filter-group">
                <label for="os-select">Операционная система:</label>
                <select id="os-select">
                    <option value="">Не выбрано</option>
                    <option value="win">Windows</option>
                    <option value="mac">macOS</option>
                    <option value="linux">Linux</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="rating-select">Рейтинг:</label>
                <select id="rating-select">
                    <option value="">Не выбрано</option>
                    <!-- Рейтинги будут добавлены динамически -->
                </select>
            </div>
            
            <div class="filter-group">
                <label for="min-positive-ratio">Минимальный % положительных отзывов:</label>
                <input type="number" id="min-positive-ratio" min="0" max="100" placeholder="Например, 80">
            </div>
            
            <div class="filter-group">
                <label for="steam-deck-select">Поддержка Steam Deck:</label>
                <select id="steam-deck-select">
                    <option value="">Не выбрано</option>
                    <option value="true">Да</option>
                    <option value="false">Нет</option>
                </select>
            </div>
        </div>
        
        <button id="filter-btn">Применить фильтры</button>
        
        <div id="results">
            <!-- Результаты фильтрации будут здесь -->
        </div>
    </div>

    <script src="/static/api-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const api = new GameRecommenderAPI();
            const filterBtn = document.getElementById('filter-btn');
            const resultsDiv = document.getElementById('results');
            const yearMinSelect = document.getElementById('year-min');
            const yearMaxSelect = document.getElementById('year-max');
            const tagSelect = document.getElementById('tag-select');
            const selectedTagsDiv = document.getElementById('selected-tags');
            const osSelect = document.getElementById('os-select');
            const ratingSelect = document.getElementById('rating-select');
            const minPositiveRatioInput = document.getElementById('min-positive-ratio');
            const steamDeckSelect = document.getElementById('steam-deck-select');
            
            // Массив выбранных тегов
            const selectedTags = [];
            
            // Загружаем доступные годы
            async function loadYears() {
                try {
                    const years = await api.getAvailableYears();
                    years.sort((a, b) => a - b);
                    
                    years.forEach(year => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        yearMinSelect.appendChild(option.cloneNode(true));
                        yearMaxSelect.appendChild(option);
                    });
                } catch (error) {
                    console.error('Error loading years:', error);
                }
            }
            
            // Загружаем доступные теги
            async function loadTags() {
                try {
                    const tags = await api.getAvailableTags();
                    tags.sort((a, b) => a.localeCompare(b));
                    
                    tags.forEach(tag => {
                        const option = document.createElement('option');
                        option.value = tag;
                        option.textContent = tag;
                        tagSelect.appendChild(option);
                    });
                } catch (error) {
                    console.error('Error loading tags:', error);
                }
            }
            
            // Загружаем доступные рейтинги
            async function loadRatings() {
                try {
                    const ratings = await api.getAvailableRatings();
                    
                    ratings.forEach(rating => {
                        const option = document.createElement('option');
                        option.value = rating;
                        option.textContent = rating;
                        ratingSelect.appendChild(option);
                    });
                } catch (error) {
                    console.error('Error loading ratings:', error);
                }
            }
            
            // Добавляем тег в список выбранных
            function addTag(tag) {
                if (tag && !selectedTags.includes(tag)) {
                    selectedTags.push(tag);
                    renderSelectedTags();
                }
                tagSelect.value = '';
            }
            
            // Удаляем тег из списка выбранных
            function removeTag(tag) {
                const index = selectedTags.indexOf(tag);
                if (index !== -1) {
                    selectedTags.splice(index, 1);
                    renderSelectedTags();
                }
            }
            
            // Отображаем выбранные теги
            function renderSelectedTags() {
                selectedTagsDiv.innerHTML = '';
                selectedTags.forEach(tag => {
                    const tagElement = document.createElement('div');
                    tagElement.className = 'tag';
                    tagElement.innerHTML = `
                        ${tag}
                        <span class="tag-remove" data-tag="${tag}">×</span>
                    `;
                    selectedTagsDiv.appendChild(tagElement);
                });
                
                // Добавляем обработчики для удаления тегов
                document.querySelectorAll('.tag-remove').forEach(btn => {
                    btn.addEventListener('click', function() {
                        removeTag(this.getAttribute('data-tag'));
                    });
                });
            }
            
            // Обработчик выбора тега
            tagSelect.addEventListener('change', function() {
                if (this.value) {
                    addTag(this.value);
                }
            });
            
            // Применяем фильтры
            filterBtn.addEventListener('click', async function() {
                resultsDiv.innerHTML = '<p>Загрузка результатов...</p>';
                
                const filters = {};
                
                if (yearMinSelect.value) filters.year_min = parseInt(yearMinSelect.value);
                if (yearMaxSelect.value) filters.year_max = parseInt(yearMaxSelect.value);
                if (selectedTags.length > 0) filters.tags = selectedTags;
                if (osSelect.value) filters.os = osSelect.value;
                if (ratingSelect.value) filters.rating = ratingSelect.value;
                if (minPositiveRatioInput.value) filters.min_positive_ratio = parseInt(minPositiveRatioInput.value);
                if (steamDeckSelect.value) filters.steam_deck = steamDeckSelect.value === 'true';
                
                try {
                    const data = await api.filterGames(filters);
                    
                    let html = '';
                    
                    // Отображаем сводку по примененным фильтрам
                    if (data.applied_filters && data.applied_filters.length > 0) {
                        html += '<div class="filter-summary">';
                        html += '<strong>Применены фильтры:</strong><br>';
                        data.applied_filters.forEach(filter => {
                            html += `<span class="filter-badge">${filter.name}: ${filter.value}</span>`;
                        });
                        html += '</div>';
                    }
                    
                    html += `<h2>Найдено игр: ${data.total}</h2>`;
                    
                    if (data.games && data.games.length > 0) {
                        html += '<div class="games-grid">';
                        data.games.forEach(game => {
                            html += `
                                <div class="game-card">
                                    <div class="game-title">${game.title}</div>
                                    <div class="game-rating">${game.rating || 'Нет рейтинга'}</div>
                                    <div class="game-positive-ratio">${game.positive_ratio ? game.positive_ratio + '% положительных отзывов' : 'Нет данных'}</div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    } else {
                        html += '<p>Игры не найдены</p>';
                    }
                    
                    resultsDiv.innerHTML = html;
                } catch (error) {
                    console.error('Error filtering games:', error);
                    resultsDiv.innerHTML = '<p>Произошла ошибка при фильтрации игр</p>';
                }
            });
            
            // Загружаем данные при загрузке страницы
            loadYears();
            loadTags();
            loadRatings();
        });
    </script>
</body>
</html>
