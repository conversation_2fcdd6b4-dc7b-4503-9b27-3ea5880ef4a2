from sqlmodel import Session, select, func
from src.database import engine
from src.models.models import BalancedRecommendation

def main():
    with Session(engine) as session:
        # Получаем уникальные ID пользователей
        users = session.exec(select(BalancedRecommendation.user_id).distinct()).all()
        print(f'Всего пользователей: {len(users)}')
        print(f'Примеры ID пользователей: {users[:5]}')

        # Получаем пользователей с небольшим количеством оценок (от 5 до 20)
        query = (
            select(
                BalancedRecommendation.user_id,
                func.count(BalancedRecommendation.id).label("ratings_count")
            )
            .group_by(BalancedRecommendation.user_id)
            .having(func.count(BalancedRecommendation.id).between(5, 20))
            .limit(20)
        )

        users_with_few_ratings = session.exec(query).all()
        print("\nПользователи с небольшим количеством оценок (от 5 до 20):")
        for user_id, ratings_count in users_with_few_ratings:
            # Проверяем, есть ли у пользователя положительные оценки
            positive_ratings = session.exec(
                select(func.count(BalancedRecommendation.id))
                .where(
                    (BalancedRecommendation.user_id == user_id) &
                    (BalancedRecommendation.is_recommended == True)
                )
            ).one()

            print(f'Пользователь {user_id} имеет {ratings_count} оценок, из них положительных: {positive_ratings}')

        # Ищем пользователей без положительных оценок
        # Сначала получаем всех пользователей с небольшим количеством оценок
        query = (
            select(
                BalancedRecommendation.user_id,
                func.count(BalancedRecommendation.id).label("ratings_count")
            )
            .group_by(BalancedRecommendation.user_id)
            .having(func.count(BalancedRecommendation.id).between(1, 10))
            .limit(100)
        )

        users_with_few_ratings = session.exec(query).all()

        # Затем проверяем каждого пользователя на наличие положительных оценок
        users_without_positive_ratings = []
        for user_id, ratings_count in users_with_few_ratings:
            positive_ratings = session.exec(
                select(func.count(BalancedRecommendation.id))
                .where(
                    (BalancedRecommendation.user_id == user_id) &
                    (BalancedRecommendation.is_recommended == True)
                )
            ).one()

            if positive_ratings == 0:
                users_without_positive_ratings.append((user_id, ratings_count, 0))
                if len(users_without_positive_ratings) >= 5:
                    break

        print("\nПользователи без положительных оценок:")
        for user_id, ratings_count, positive_count in users_without_positive_ratings:
            print(f'Пользователь {user_id} имеет {ratings_count} оценок, из них положительных: {positive_count}')

if __name__ == "__main__":
    main()
