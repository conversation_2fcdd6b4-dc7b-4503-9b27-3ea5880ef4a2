"""
Обновление таблицы users в базе данных
"""
import os
import sqlite3

def main():
    """Основная функция обновления таблицы users"""
    # Пути к базам данных
    db_paths = [
        "src/database.db",
        "data/games.db"
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"Обновление таблицы users в базе данных {db_path}...")
            update_users_table(db_path)
        else:
            print(f"База данных не найдена: {db_path}")

def update_users_table(db_path):
    """Обновление таблицы users"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем, существует ли таблица users
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print(f"Таблица users не найдена в базе данных {db_path}")
            conn.close()
            return
        
        # Получаем текущую структуру таблицы users
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"Текущая структура таблицы users:")
        for col in columns:
            print(f"- {col[1]} ({col[2]})")
        
        # Проверяем, есть ли колонка is_active
        if "is_active" not in column_names:
            print("Добавление колонки is_active...")
            cursor.execute("ALTER TABLE users ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT 1")
            print("Колонка is_active успешно добавлена")
        else:
            print("Колонка is_active уже существует")
        
        # Проверяем, есть ли колонка hashed_password
        if "hashed_password" not in column_names:
            print("Добавление колонки hashed_password...")
            cursor.execute("ALTER TABLE users ADD COLUMN hashed_password TEXT")
            print("Колонка hashed_password успешно добавлена")
        else:
            print("Колонка hashed_password уже существует")
        
        # Получаем обновленную структуру таблицы users
        cursor.execute("PRAGMA table_info(users)")
        updated_columns = cursor.fetchall()
        
        print(f"Обновленная структура таблицы users:")
        for col in updated_columns:
            print(f"- {col[1]} ({col[2]})")
        
        conn.commit()
        conn.close()
        print(f"Таблица users в базе данных {db_path} успешно обновлена")
    except Exception as e:
        print(f"Ошибка при обновлении таблицы users: {str(e)}")

if __name__ == "__main__":
    main()
