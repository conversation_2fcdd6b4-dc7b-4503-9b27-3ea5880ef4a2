# Настройка переменных окружения

## Обзор

Проект использует переменные окружения для конфигурации всех основных настроек. Это обеспечивает гибкость развертывания и безопасность.

## Быстрый старт

1. **Скопируйте шаблон конфигурации:**
   ```bash
   cp .env.example .env
   ```

2. **Отредактируйте файл `.env`** под ваши нужды

3. **Запустите приложение:**
   ```bash
   python run.py
   ```

## Структура конфигурации

### 🔐 Безопасность
```env
SECRET_KEY=your-super-secret-key-change-this-in-production-min-32-chars
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 🌐 Сервер
```env
HOST=127.0.0.1
PORT=8000
ENVIRONMENT=development  # development, production, testing
DEBUG=true
```

### 🗄️ База данных
```env
DATABASE_URL=sqlite:///src/database.db
GAMES_DATABASE_PATH=data/games.db
DATABASE_ECHO=true
```

### 💾 Кэширование
```env
CACHE_DIR=src/cache
ENABLE_CACHE=true
```

### 🌍 CORS
```env
CORS_ORIGINS=*
CORS_METHODS=*
CORS_HEADERS=*
CORS_CREDENTIALS=true
```

### 🤖 Машинное обучение
```env
MIN_USER_RATINGS=5
MIN_GAME_RATINGS=5
SVD_COMPONENTS=200
```

### 📁 Загрузка файлов
```env
MAX_AVATAR_SIZE=2097152  # 2MB в байтах
ALLOWED_AVATAR_TYPES=image/jpeg,image/png,image/gif
```

### 📊 API
```env
MAX_PAGE_SIZE=1000
MAX_RECOMMENDATIONS=20
API_TIMEOUT=30.0
STEAM_API_TIMEOUT=10.0
```

### 📝 Логирование
```env
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT=simple
```

## Режимы работы

### Development (Разработка)
```env
ENVIRONMENT=development
DEBUG=true
DATABASE_ECHO=true
LOG_LEVEL=DEBUG
```

### Production (Продакшн)
```env
ENVIRONMENT=production
DEBUG=false
DATABASE_ECHO=false
LOG_LEVEL=WARNING
SECRET_KEY=super-secure-production-key-min-32-chars
CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com
```

### Testing (Тестирование)
```env
ENVIRONMENT=testing
DEBUG=false
DATABASE_URL=sqlite:///test_database.db
LOG_LEVEL=ERROR
```

## Безопасность

### ⚠️ Важные настройки для продакшена:

1. **SECRET_KEY**: Обязательно измените на уникальный ключ длиной минимум 32 символа
2. **CORS_ORIGINS**: Укажите конкретные домены вместо "*"
3. **DEBUG**: Установите в `false`
4. **DATABASE_ECHO**: Установите в `false` для производительности

### 🔒 Генерация безопасного SECRET_KEY:

```python
import secrets
print(secrets.token_urlsafe(32))
```

## Валидация настроек

Приложение автоматически проверяет:
- ✅ SECRET_KEY минимум 32 символа
- ✅ PORT в диапазоне 1-65535
- ✅ ENVIRONMENT в списке допустимых значений
- ✅ LOG_LEVEL корректный

## Использование в коде

```python
from src.config import settings

# Доступ к настройкам
print(f"Сервер: {settings.HOST}:{settings.PORT}")
print(f"Режим: {settings.ENVIRONMENT}")

# Проверка режима
if settings.is_development:
    print("Режим разработки")

# Списки значений
allowed_types = settings.allowed_avatar_types_list
cors_origins = settings.cors_origins_list
```

## Переменные окружения в системе

Вместо файла `.env` можно использовать системные переменные:

### Windows:
```cmd
set SECRET_KEY=your-secret-key
set PORT=8000
python run.py
```

### Linux/macOS:
```bash
export SECRET_KEY=your-secret-key
export PORT=8000
python run.py
```

## Docker

Для Docker используйте файл `.env` или передавайте переменные:

```dockerfile
ENV SECRET_KEY=your-secret-key
ENV ENVIRONMENT=production
ENV DEBUG=false
```

## Устранение проблем

### Проблема: "PydanticImportError: BaseSettings has been moved"
**Решение**: Убедитесь, что установлен `pydantic-settings`:
```bash
pip install pydantic-settings
```

### Проблема: "SECRET_KEY должен быть не менее 32 символов"
**Решение**: Увеличьте длину SECRET_KEY в файле `.env`

### Проблема: Приложение не видит .env файл
**Решение**: Убедитесь, что файл `.env` находится в корне проекта

## Примеры конфигураций

### Локальная разработка
```env
SECRET_KEY=dev-key-12345678901234567890123456
HOST=127.0.0.1
PORT=8000
ENVIRONMENT=development
DEBUG=true
```

### Продакшн сервер
```env
SECRET_KEY=prod-super-secure-key-32-chars-minimum
HOST=0.0.0.0
PORT=80
ENVIRONMENT=production
DEBUG=false
CORS_ORIGINS=https://myapp.com
DATABASE_ECHO=false
LOG_LEVEL=WARNING
```

### Тестовый сервер
```env
SECRET_KEY=test-key-12345678901234567890123456
HOST=127.0.0.1
PORT=8001
ENVIRONMENT=testing
DEBUG=false
DATABASE_URL=sqlite:///test.db
LOG_LEVEL=ERROR
```
