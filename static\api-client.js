/**
 * API-клиент для взаимодействия с бэкендом рекомендательной системы
 */
class GameRecommenderAPI {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl;
    }

    /**
     * Получает рекомендации для игры по её названию
     * @param {string} title - Название игры
     * @param {number} topN - Количество рекомендаций (от 1 до 20)
     * @returns {Promise<Object>} - Объект с рекомендациями
     */
    async getRecommendations(title, topN = 10) {
        try {
            const response = await fetch(`${this.baseUrl}/recommend/${encodeURIComponent(title)}?top_n=${topN}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching recommendations:', error);
            throw error;
        }
    }

    /**
     * Получает список всех игр
     * @param {number} limit - Максимальное количество игр
     * @returns {Promise<Array>} - Массив игр
     */
    async getAllGames(limit = 100) {
        try {
            const response = await fetch(`${this.baseUrl}/games?limit=${limit}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching games:', error);
            throw error;
        }
    }

    /**
     * Получает информацию об игре по её ID
     * @param {number} appId - ID игры
     * @returns {Promise<Object>} - Объект с информацией об игре
     */
    async getGameById(appId) {
        try {
            const response = await fetch(`${this.baseUrl}/games/${appId}`);
            return await response.json();
        } catch (error) {
            console.error(`Error fetching game with ID ${appId}:`, error);
            throw error;
        }
    }

    /**
     * Поиск игр по названию
     * @param {string} title - Название игры
     * @returns {Promise<Array>} - Массив найденных игр
     */
    async searchGames(title) {
        try {
            const response = await fetch(`${this.baseUrl}/games/search/${encodeURIComponent(title)}`);
            return await response.json();
        } catch (error) {
            console.error('Error searching games:', error);
            throw error;
        }
    }

    /**
     * Автокомплит названий игр
     * @param {string} query - Строка запроса
     * @param {number} limit - Максимальное количество результатов
     * @returns {Promise<Array>} - Массив названий игр для автокомплита
     */
    async autocompleteGameTitle(query, limit = 10) {
        try {
            const response = await fetch(`${this.baseUrl}/games/autocomplete?query=${encodeURIComponent(query)}&limit=${limit}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching autocomplete:', error);
            throw error;
        }
    }

    /**
     * Получает список доступных тегов
     * @param {number} limit - Максимальное количество тегов
     * @returns {Promise<Array>} - Массив тегов
     */
    async getAvailableTags(limit = 100) {
        try {
            const response = await fetch(`${this.baseUrl}/games/filter/tags?limit=${limit}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching tags:', error);
            throw error;
        }
    }

    /**
     * Получает список доступных годов выпуска
     * @returns {Promise<Array>} - Массив годов
     */
    async getAvailableYears() {
        try {
            const response = await fetch(`${this.baseUrl}/games/filter/years`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching years:', error);
            throw error;
        }
    }

    /**
     * Получает список доступных рейтингов
     * @returns {Promise<Array>} - Массив рейтингов
     */
    async getAvailableRatings() {
        try {
            const response = await fetch(`${this.baseUrl}/games/filter/ratings`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching ratings:', error);
            throw error;
        }
    }

    /**
     * Фильтрует игры по заданным критериям
     * @param {Object} filters - Объект с фильтрами
     * @returns {Promise<Object>} - Объект с результатами фильтрации
     */
    async filterGames(filters = {}) {
        try {
            const queryParams = new URLSearchParams();

            // Диапазон годов
            if (filters.year_min) queryParams.append('year_min', filters.year_min);
            if (filters.year_max) queryParams.append('year_max', filters.year_max);

            // Несколько тегов
            if (filters.tags && Array.isArray(filters.tags)) {
                filters.tags.forEach(tag => {
                    if (tag) queryParams.append('tags', tag);
                });
            } else if (filters.tag) {
                // Для обратной совместимости
                queryParams.append('tags', filters.tag);
            }

            if (filters.os) queryParams.append('os', filters.os);
            if (filters.rating) queryParams.append('rating', filters.rating);
            if (filters.min_positive_ratio) queryParams.append('min_positive_ratio', filters.min_positive_ratio);
            if (filters.steam_deck !== undefined) queryParams.append('steam_deck', filters.steam_deck);

            const response = await fetch(`${this.baseUrl}/games/filter?${queryParams.toString()}`);
            return await response.json();
        } catch (error) {
            console.error('Error filtering games:', error);
            throw error;
        }
    }
}
