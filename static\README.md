# Static Files Directory

Эта папка содержит статические файлы для веб-интерфейса системы рекомендаций игр.

## Файлы

### index.html
Главная страница веб-интерфейса с функциональностью:
- Поиск игр с автокомплитом
- Получение рекомендаций по названию игры
- Отображение информации об играх
- Простой и интуитивный интерфейс

### filter.html
Страница для фильтрации игр с возможностями:
- Фильтрация по тегам
- Фильтрация по году выпуска
- Фильтрация по рейтингу
- Фильтрация по операционной системе
- Фильтрация по поддержке Steam Deck

### api-client.js
JavaScript клиент для взаимодействия с API:
- Функции для выполнения HTTP запросов
- Обработка ответов от сервера
- Утилиты для работы с данными
- Интеграция с HTML страницами

## Доступ

Статические файлы доступны по следующим URL после запуска сервера:
- http://localhost:8000/ - перенаправляет на главную страницу
- http://localhost:8000/static/index.html - главная страница
- http://localhost:8000/static/filter.html - страница фильтрации

## Разработка

При разработке фронтенда:
1. Изменения в HTML/CSS/JS файлах применяются сразу
2. Не требуется перезапуск сервера
3. Можно использовать инструменты разработчика браузера для отладки

## Расширение

Для добавления новых страниц:
1. Создайте новый HTML файл в этой папке
2. Подключите api-client.js для взаимодействия с API
3. Добавьте ссылки на новую страницу в существующие файлы
