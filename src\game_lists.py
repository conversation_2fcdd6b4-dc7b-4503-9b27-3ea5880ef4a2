from typing import List, Dict, Any, Optional
from sqlmodel import Session, select
from src.models.models import GameList, Game, GameListType

def add_game_to_list(user_id: int, app_id: int, list_type: GameListType, session: Session) -> Dict[str, Any]:
    """
    Добавляет игру в список пользователя

    Args:
        user_id: ID пользователя
        app_id: ID игры
        list_type: Тип списка (playing, planned, completed)
        session: Сессия базы данных

    Returns:
        Словарь с результатом операции
    """
    try:
        # Проверяем, что игра существует
        game = session.exec(select(Game).where(Game.app_id == app_id)).first()
        if not game:
            return {"error": f"Игра с ID {app_id} не найдена"}

        # Проверяем, есть ли уже игра в каком-либо списке
        existing_list = session.exec(
            select(GameList).where(
                (GameList.user_id == user_id) & 
                (GameList.app_id == app_id)
            )
        ).first()

        if existing_list:
            # Если игра уже в списке, обновляем тип списка
            existing_list.list_type = list_type
            session.add(existing_list)
            session.commit()
            message = f"Игра перемещена в список '{list_type.value}'"
        else:
            # Добавляем игру в список
            new_list_item = GameList(
                user_id=user_id,
                app_id=app_id,
                list_type=list_type
            )
            session.add(new_list_item)
            session.commit()
            message = f"Игра добавлена в список '{list_type.value}'"

        return {
            "success": True,
            "message": message,
            "user_id": user_id,
            "app_id": app_id,
            "list_type": list_type.value,
            "title": game.title
        }
    except Exception as e:
        return {"error": f"Ошибка при добавлении игры в список: {str(e)}"}

def remove_game_from_list(user_id: int, app_id: int, session: Session) -> Dict[str, Any]:
    """
    Удаляет игру из списка пользователя

    Args:
        user_id: ID пользователя
        app_id: ID игры
        session: Сессия базы данных

    Returns:
        Словарь с результатом операции
    """
    try:
        # Проверяем, есть ли игра в списке
        list_item = session.exec(
            select(GameList).where(
                (GameList.user_id == user_id) & 
                (GameList.app_id == app_id)
            )
        ).first()

        if not list_item:
            return {"error": f"Игра с ID {app_id} не найдена в ваших списках"}

        # Удаляем игру из списка
        session.delete(list_item)
        session.commit()

        return {
            "success": True,
            "message": "Игра удалена из списка",
            "user_id": user_id,
            "app_id": app_id
        }
    except Exception as e:
        return {"error": f"Ошибка при удалении игры из списка: {str(e)}"}

def get_user_game_lists(user_id: int, session: Session) -> Dict[str, List[Dict[str, Any]]]:
    """
    Получает все списки игр пользователя

    Args:
        user_id: ID пользователя
        session: Сессия базы данных

    Returns:
        Словарь со списками игр пользователя
    """
    try:
        # Получаем все списки пользователя
        lists_query = select(GameList, Game).join(Game).where(GameList.user_id == user_id)
        lists_result = session.exec(lists_query).all()

        # Группируем игры по типу списка
        playing = []
        planned = []
        completed = []

        for list_item, game in lists_result:
            game_info = {
                "app_id": game.app_id,
                "title": game.title,
                "added_at": list_item.added_at.isoformat()
            }

            if list_item.list_type == GameListType.PLAYING:
                playing.append(game_info)
            elif list_item.list_type == GameListType.PLANNED:
                planned.append(game_info)
            elif list_item.list_type == GameListType.COMPLETED:
                completed.append(game_info)

        return {
            "playing": playing,
            "planned": planned,
            "completed": completed
        }
    except Exception as e:
        return {
            "error": f"Ошибка при получении списков игр: {str(e)}",
            "playing": [],
            "planned": [],
            "completed": []
        }

def get_games_by_list_type(user_id: int, list_type: GameListType, session: Session) -> List[Dict[str, Any]]:
    """
    Получает игры пользователя по типу списка

    Args:
        user_id: ID пользователя
        list_type: Тип списка
        session: Сессия базы данных

    Returns:
        Список игр в указанном списке
    """
    try:
        # Получаем игры из указанного списка
        query = select(GameList, Game).join(Game).where(
            (GameList.user_id == user_id) & 
            (GameList.list_type == list_type)
        )
        result = session.exec(query).all()

        # Форматируем результат
        games = [
            {
                "app_id": game.app_id,
                "title": game.title,
                "added_at": list_item.added_at.isoformat()
            }
            for list_item, game in result
        ]

        return games
    except Exception as e:
        print(f"Ошибка при получении игр из списка {list_type.value}: {str(e)}")
        return []
