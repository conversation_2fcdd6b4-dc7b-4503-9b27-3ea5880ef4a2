import os
import pickle
import json
import time
import pandas as pd
from datetime import datetime
from sqlmodel import Session, select, delete
from src.database import engine, create_db_and_tables
from src.models.models import Game, BalancedRecommendation

# Пути для кеширования
CACHE_DIR = "src/cache"
BALANCED_RECOMMENDATIONS_CACHE = os.path.join(CACHE_DIR, "balanced_recommendations_cache.pkl")

def load_balanced_recommendations_from_cache():
    """Загружает сбалансированные рекомендации из кэша"""
    if not os.path.exists(BALANCED_RECOMMENDATIONS_CACHE):
        print("Ошибка: Кэш сбалансированных рекомендаций не найден")
        return None

    try:
        with open(BALANCED_RECOMMENDATIONS_CACHE, 'rb') as f:
            recommendations = pickle.load(f)
        print(f"Загружено {len(recommendations)} сбалансированных рекомендаций из кэша")
        return recommendations
    except Exception as e:
        print(f"Ошибка при загрузке сбалансированных рекомендаций из кэша: {str(e)}")
        return None

def save_balanced_recommendations_to_db(recommendations_df):
    """Сохраняет сбалансированные рекомендации в базу данных"""
    if recommendations_df is None or len(recommendations_df) == 0:
        print("Нет данных для сохранения в базу данных")
        return False

    try:
        # Создаем базу данных и таблицы, если они не существуют
        create_db_and_tables()

        # Проверяем, что все игры существуют в базе данных
        with Session(engine) as session:
            # Получаем список всех app_id из базы данных
            db_app_ids = set(game.app_id for game in session.exec(select(Game)))

            # Фильтруем рекомендации, оставляя только те, которые относятся к существующим играм
            valid_recommendations = recommendations_df[recommendations_df['app_id'].isin(db_app_ids)]

            if len(valid_recommendations) < len(recommendations_df):
                print(f"Предупреждение: {len(recommendations_df) - len(valid_recommendations)} рекомендаций относятся к несуществующим играм и будут пропущены")

            # Очищаем таблицу сбалансированных рекомендаций
            print("Очистка таблицы сбалансированных рекомендаций...")
            session.exec(delete(BalancedRecommendation))
            session.commit()

            # Сохраняем сбалансированные рекомендации в базу данных
            print(f"Сохранение {len(valid_recommendations)} сбалансированных рекомендаций в базу данных...")
            batch_size = 1000
            total_batches = (len(valid_recommendations) + batch_size - 1) // batch_size

            for i in range(0, len(valid_recommendations), batch_size):
                batch = valid_recommendations.iloc[i:i+batch_size]
                balanced_recommendations = [
                    BalancedRecommendation(
                        app_id=int(row['app_id']),
                        user_id=int(row['user_id']),
                        review_id=int(row['review_id']) if pd.notna(row['review_id']) else None,
                        is_recommended=bool(row['is_recommended']),
                        helpful=int(row['helpful']) if pd.notna(row['helpful']) else None,
                        funny=int(row['funny']) if pd.notna(row['funny']) else None,
                        date=row['date'],
                        hours=float(row['hours']) if pd.notna(row['hours']) else None
                    )
                    for _, row in batch.iterrows()
                ]

                session.add_all(balanced_recommendations)
                session.commit()

                batch_num = i // batch_size + 1
                print(f"Сохранена партия {batch_num}/{total_batches} ({len(batch)} рекомендаций)")

            # Проверяем количество сохраненных рекомендаций
            count = len(list(session.exec(select(BalancedRecommendation))))
            print(f"Всего сохранено {count} сбалансированных рекомендаций в базу данных")

            return True
    except Exception as e:
        print(f"Ошибка при сохранении сбалансированных рекомендаций в базу данных: {str(e)}")
        return False

def main():
    """Основная функция для переноса сбалансированных рекомендаций из кэша в базу данных"""
    start_time = time.time()

    print("Начало переноса сбалансированных рекомендаций из кэша в базу данных...")

    # Загружаем сбалансированные рекомендации из кэша
    recommendations = load_balanced_recommendations_from_cache()
    if recommendations is None:
        print("Не удалось загрузить сбалансированные рекомендации из кэша")
        return

    # Сохраняем сбалансированные рекомендации в базу данных
    db_success = save_balanced_recommendations_to_db(recommendations)

    end_time = time.time()
    print(f"Перенос сбалансированных рекомендаций завершен за {end_time - start_time:.2f} секунд")

    if db_success:
        print("Сбалансированные рекомендации успешно перенесены из кэша в базу данных")
    else:
        print("Не удалось перенести сбалансированные рекомендации из кэша в базу данных")

if __name__ == "__main__":
    main()
