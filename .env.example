# =============================================================================
# GAME RECOMMENDATION SYSTEM - ENVIRONMENT VARIABLES
# =============================================================================
# Скопируйте этот файл в .env и настройте значения для вашего окружения

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# Секретный ключ для JWT токенов (ОБЯЗАТЕЛЬНО ИЗМЕНИТЕ В ПРОДАКШЕНЕ!)
SECRET_KEY=your-super-secret-key-change-this-in-production-min-32-chars

# Время жизни токена доступа в минутах
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# SERVER SETTINGS
# =============================================================================
# Хост и порт для запуска сервера
HOST=127.0.0.1
PORT=8000

# Режим работы: development, production, testing
ENVIRONMENT=development

# Включить/выключить отладочный режим
DEBUG=true

# =============================================================================
# DATABASE SETTINGS
# =============================================================================
# Основная база данных приложения
DATABASE_URL=sqlite:///src/database.db

# База данных с играми Steam
GAMES_DATABASE_PATH=data/games.db

# Включить логирование SQL запросов
DATABASE_ECHO=true

# =============================================================================
# CACHE SETTINGS
# =============================================================================
# Директория для кэш файлов
CACHE_DIR=src/cache

# Включить/выключить кэширование
ENABLE_CACHE=true

# =============================================================================
# CORS SETTINGS
# =============================================================================
# Разрешенные домены для CORS (разделенные запятыми)
# В продакшене укажите конкретные домены вместо "*"
CORS_ORIGINS=*

# Разрешенные методы HTTP
CORS_METHODS=*

# Разрешенные заголовки
CORS_HEADERS=*

# Разрешить передачу cookies
CORS_CREDENTIALS=true

# =============================================================================
# EXTERNAL APIs
# =============================================================================
# Steam API настройки
STEAM_API_TIMEOUT=10.0

# =============================================================================
# LOGGING SETTINGS
# =============================================================================
# Уровень логирования: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Формат логов: simple, detailed, json
LOG_FORMAT=simple

# =============================================================================
# MACHINE LEARNING SETTINGS
# =============================================================================
# Минимальное количество оценок пользователя для рекомендаций
MIN_USER_RATINGS=5

# Минимальное количество оценок игры для включения в систему
MIN_GAME_RATINGS=5

# Количество компонентов для SVD
SVD_COMPONENTS=200

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================
# Максимальный размер аватара в байтах (2MB)
MAX_AVATAR_SIZE=2097152

# Разрешенные типы файлов для аватаров
ALLOWED_AVATAR_TYPES=image/jpeg,image/png,image/gif

# =============================================================================
# API SETTINGS
# =============================================================================
# Максимальное количество результатов на страницу
MAX_PAGE_SIZE=1000

# Максимальное количество рекомендаций
MAX_RECOMMENDATIONS=20

# Таймаут для внешних API запросов в секундах
API_TIMEOUT=30.0
